import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip
} from '@mui/material';
import {
  Launch as LaunchIcon,
  List as ListIcon,
  BugReport as BugReportIcon,
  Rocket as RocketIcon
} from '@mui/icons-material';
import { COLORS, TYPOGRAPHY } from '../themes/designSystem';

const QuickAccessButtons = () => {
  const navigate = useNavigate();

  const quickLinks = [
    {
      title: 'Liste des Commandes Enhanced',
      description: 'Nouvelle liste avec API live et StandardTable',
      url: '/app/orders',
      icon: <ListIcon />,
      color: COLORS.primary.main,
      badge: 'NEW'
    },
    {
      title: 'Démo API Live',
      description: 'Démonstration complète avec documentation',
      url: '/app/orders-demo',
      icon: <RocketIcon />,
      color: COLORS.success.main,
      badge: 'DEMO'
    },
    {
      title: 'API Direct',
      description: 'Accès direct à l\'API Laravel',
      url: 'https://laravel-api.fly.dev/api/commandes',
      icon: <LaunchIcon />,
      color: COLORS.info.main,
      badge: 'API',
      external: true
    }
  ];

  const handleNavigation = (link) => {
    if (link.external) {
      window.open(link.url, '_blank');
    } else {
      navigate(link.url);
    }
  };

  return (
    <Card sx={{ mb: 3, border: `2px solid ${COLORS.primary.main}` }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <RocketIcon sx={{ color: COLORS.primary.main, mr: 1 }} />
          <Typography 
            variant="h5" 
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.primary.main
            }}
          >
            🚀 Accès Rapide - Nouvelles Fonctionnalités
          </Typography>
        </Box>

        <Typography 
          variant="body2" 
          sx={{ 
            color: COLORS.text.secondary, 
            mb: 3,
            fontFamily: TYPOGRAPHY.fontFamily.primary
          }}
        >
          Accédez rapidement aux nouvelles pages de gestion des commandes avec l'API live
        </Typography>

        <Grid container spacing={2}>
          {quickLinks.map((link, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card 
                sx={{ 
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  border: `1px solid ${COLORS.grey[300]}`,
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: `0 8px 25px ${link.color}20`,
                    borderColor: link.color
                  }
                }}
                onClick={() => handleNavigation(link)}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ color: link.color }}>
                        {link.icon}
                      </Box>
                      <Typography 
                        variant="h6" 
                        sx={{
                          fontFamily: TYPOGRAPHY.fontFamily.primary,
                          fontWeight: TYPOGRAPHY.fontWeight.semibold,
                          fontSize: TYPOGRAPHY.fontSize.md
                        }}
                      >
                        {link.title}
                      </Typography>
                    </Box>
                    <Chip 
                      label={link.badge} 
                      size="small" 
                      sx={{ 
                        backgroundColor: link.color,
                        color: 'white',
                        fontWeight: TYPOGRAPHY.fontWeight.bold
                      }} 
                    />
                  </Box>
                  
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: COLORS.text.secondary,
                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                      mb: 2
                    }}
                  >
                    {link.description}
                  </Typography>

                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ 
                      borderColor: link.color,
                      color: link.color,
                      '&:hover': {
                        backgroundColor: `${link.color}10`,
                        borderColor: link.color
                      }
                    }}
                    endIcon={link.external ? <LaunchIcon /> : null}
                  >
                    {link.external ? 'Ouvrir' : 'Accéder'}
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 3, p: 2, backgroundColor: COLORS.grey[50], borderRadius: 1 }}>
          <Typography variant="body2" sx={{ color: COLORS.text.secondary, textAlign: 'center' }}>
            💡 <strong>Astuce :</strong> Vous pouvez aussi accéder directement via les URLs :
            <br />
            • <code>/app/orders</code> - Liste Enhanced
            • <code>/app/orders-demo</code> - Démo complète
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default QuickAccessButtons;
