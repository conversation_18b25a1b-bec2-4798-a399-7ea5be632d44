# 📦 Documentation Complète - Gestion des Produits

## 🎯 Vue d'Ensemble

Cette documentation détaille l'API complète de gestion des produits, incluant les variantes, attributs et images. Le système utilise Laravel avec une architecture polymorphe pour la gestion des images et un système d'attributs flexible.

## 📋 Table des Matières

1. [Gestion des Produits](#-gestion-des-produits)
2. [Gestion des Variantes](#-gestion-des-variantes)
3. [Gestion des Attributs](#-gestion-des-attributs)
4. [Gestion des Images](#-gestion-des-images)
5. [Structures de Données](#-structures-de-données)
6. [Codes d'Erreur](#-codes-derreur)

---

## 🛍️ Gestion des Produits

### **Lister les Produits**

```http
GET /api/produits
```

**Paramètres de requête :**
- `page` (optionnel) : Numéro de page (défaut: 1)
- `per_page` (optionnel) : Nombre d'éléments par page (défaut: 15, max: 100)
- `marque_id` (optionnel) : Filtrer par marque
- `sous_sous_categorie_id` (optionnel) : Filtrer par sous-sous-catégorie
- `prix_min` (optionnel) : Prix minimum
- `prix_max` (optionnel) : Prix maximum
- `en_stock` (optionnel) : `true` pour les produits en stock uniquement

**Exemple de requête :**
```bash
curl -X GET "https://api.example.com/api/produits?page=1&per_page=20&marque_id=5&en_stock=true" \
  -H "Accept: application/json"
```

**Réponse :**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "nom_produit": "Smartphone Galaxy S24",
      "description_produit": "Dernier smartphone Samsung avec écran AMOLED",
      "prix_produit": "899.99",
      "quantite_produit": 50,
      "reference": "5-1",
      "marque": {
        "id": 5,
        "nom_marque": "Samsung"
      },
      "sousSousCategorie": {
        "id": 12,
        "nom_sous_sous_categorie": "Smartphones",
        "sousCategorie": {
          "id": 3,
          "nom_sous_categorie": "Téléphones",
          "categorie": {
            "id": 1,
            "nom_categorie": "Électronique"
          }
        }
      },
      "images": [
        {
          "id": 1,
          "path": "produits/1/galaxy-s24-front.jpg",
          "is_primary": true,
          "alt_text": "Vue de face du Galaxy S24",
          "order": 1
        }
      ],
      "variantes": [
        {
          "id": 1,
          "sku": "GAL-S24-128-BLK",
          "prix_supplement": "0.00",
          "stock": 25,
          "actif": true
        }
      ]
    }
  ],
  "pagination": {
    "current_page": 1,
    "last_page": 5,
    "per_page": 20,
    "total": 95,
    "has_more_pages": true
  },
  "cache_info": {
    "served_from_cache": false,
    "caching_enabled": false
  }
}
```

### **Afficher un Produit**

```http
GET /api/produits/{id}
```

**Paramètres :**
- `id` (requis) : ID du produit

**Exemple de requête :**
```bash
curl -X GET "https://api.example.com/api/produits/1" \
  -H "Accept: application/json"
```

**Réponse :**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "nom_produit": "Smartphone Galaxy S24",
    "description_produit": "Dernier smartphone Samsung avec écran AMOLED 6.2 pouces",
    "prix_produit": "899.99",
    "quantite_produit": 50,
    "reference": "5-1",
    "created_at": "2024-01-15T10:30:00.000000Z",
    "updated_at": "2024-01-20T14:45:00.000000Z",
    "marque": {
      "id": 5,
      "nom_marque": "Samsung",
      "logo_marque": "marques/samsung-logo.png"
    },
    "sousSousCategorie": {
      "id": 12,
      "nom_sous_sous_categorie": "Smartphones"
    },
    "collections": [
      {
        "id": 2,
        "nom": "Nouveautés 2024",
        "pivot": {
          "ordre": 1,
          "featured": true
        }
      }
    ],
    "promotions": [
      {
        "id": 3,
        "nom": "Promo Printemps",
        "pourcentage_remise": "15.00",
        "statut": "active"
      }
    ],
    "valeurs": [
      {
        "id": 1,
        "attribut_id": 1,
        "valeur": "128GB",
        "attribut": {
          "id": 1,
          "nom": "Stockage",
          "type_valeur": "texte"
        }
      }
    ],
    "variantes": [
      {
        "id": 1,
        "sku": "GAL-S24-128-BLK",
        "prix_supplement": "0.00",
        "stock": 25,
        "actif": true,
        "valeurs": [
          {
            "attribut_id": 2,
            "valeur": "Noir",
            "attribut": {
              "nom": "Couleur"
            }
          }
        ]
      }
    ],
    "images": [
      {
        "id": 1,
        "path": "produits/1/galaxy-s24-front.jpg",
        "is_primary": true,
        "alt_text": "Vue de face du Galaxy S24",
        "title": "Galaxy S24 - Face avant",
        "order": 1,
        "url": "/api/images/serve/1",
        "thumbnail_small": "/api/images/thumbnail/1/small",
        "thumbnail_medium": "/api/images/thumbnail/1/medium",
        "thumbnail_large": "/api/images/thumbnail/1/large"
      }
    ]
  }
}
```

### **Créer un Produit**

```http
POST /api/produits
```

**Corps de la requête :**
```json
{
  "nom_produit": "iPhone 15 Pro",
  "description_produit": "Nouveau iPhone avec puce A17 Pro et appareil photo 48MP",
  "prix_produit": 1199.99,
  "quantite_produit": 30,
  "marque_id": 1,
  "sous_sous_categorie_id": 12,
  "image_produit": "https://example.com/iphone15pro.jpg"
}
```

**Règles de validation :**
- `nom_produit` : requis, chaîne, max 255 caractères
- `description_produit` : optionnel, chaîne, max 5000 caractères
- `prix_produit` : requis, numérique, min 0, max 999999
- `image_produit` : optionnel, URL valide, max 2000 caractères
- `quantite_produit` : requis, entier, min 0, max 99999
- `marque_id` : requis, doit exister dans la table marques
- `sous_sous_categorie_id` : requis, doit exister dans la table sous_sous_categories

**Exemple de requête :**
```bash
curl -X POST "https://api.example.com/api/produits" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "nom_produit": "iPhone 15 Pro",
    "description_produit": "Nouveau iPhone avec puce A17 Pro",
    "prix_produit": 1199.99,
    "quantite_produit": 30,
    "marque_id": 1,
    "sous_sous_categorie_id": 12
  }'
```

**Réponse (201 Created) :**
```json
{
  "status": "success",
  "data": {
    "id": 25,
    "nom_produit": "iPhone 15 Pro",
    "description_produit": "Nouveau iPhone avec puce A17 Pro",
    "prix_produit": "1199.99",
    "quantite_produit": 30,
    "reference": "1-25",
    "marque_id": 1,
    "sous_sous_categorie_id": 12,
    "created_at": "2024-01-25T15:30:00.000000Z",
    "updated_at": "2024-01-25T15:30:00.000000Z",
    "marque": {
      "id": 1,
      "nom_marque": "Apple"
    },
    "sousSousCategorie": {
      "id": 12,
      "nom_sous_sous_categorie": "Smartphones"
    }
  },
  "message": "Product created successfully"
}
```

### **Mettre à Jour un Produit**

```http
PUT /api/produits/{id}
```

**Paramètres :**
- `id` (requis) : ID du produit à modifier

**Corps de la requête :**
```json
{
  "nom_produit": "iPhone 15 Pro Max",
  "prix_produit": 1299.99,
  "quantite_produit": 25
}
```

**Règles de validation :**
- `nom_produit` : parfois requis, chaîne, max 255 caractères
- `description_produit` : optionnel, chaîne, max 5000 caractères
- `prix_produit` : parfois requis, numérique, min 0, max 999999
- `quantite_produit` : parfois requis, entier, min 0, max 99999
- `marque_id` : parfois requis, doit exister dans la table marques
- `sous_sous_categorie_id` : parfois requis, doit exister dans la table sous_sous_categories

**Exemple de requête :**
```bash
curl -X PUT "https://api.example.com/api/produits/25" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "nom_produit": "iPhone 15 Pro Max",
    "prix_produit": 1299.99
  }'
```

**Réponse :**
```json
{
  "status": "success",
  "data": {
    "id": 25,
    "nom_produit": "iPhone 15 Pro Max",
    "prix_produit": "1299.99",
    "updated_at": "2024-01-25T16:45:00.000000Z"
  },
  "message": "Product updated successfully"
}
```

### **Supprimer un Produit**

```http
DELETE /api/produits/{id}
```

**Paramètres :**
- `id` (requis) : ID du produit à supprimer

**Exemple de requête :**
```bash
curl -X DELETE "https://api.example.com/api/produits/25" \
  -H "Accept: application/json"
```

**Réponse (200 OK) :**
```json
{
  "status": "success",
  "message": "Product deleted successfully"
}
```

---

## 🔄 Gestion des Variantes

### **Lister les Variantes d'un Produit**

```http
GET /api/produits/{produit_id}/variantes
```

**Paramètres :**
- `produit_id` (requis) : ID du produit parent
- `actif` (optionnel) : `true` pour les variantes actives uniquement

**Exemple de requête :**
```bash
curl -X GET "https://api.example.com/api/produits/1/variantes?actif=true" \
  -H "Accept: application/json"
```

**Réponse :**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "sku": "GAL-S24-128-BLK",
      "prix_supplement": "0.00",
      "stock": 25,
      "actif": true,
      "created_at": "2024-01-15T10:30:00.000000Z",
      "valeurs": [
        {
          "id": 1,
          "attribut_id": 2,
          "valeur": "Noir",
          "attribut": {
            "id": 2,
            "nom": "Couleur",
            "type_valeur": "texte"
          }
        },
        {
          "id": 2,
          "attribut_id": 3,
          "valeur": "128GB",
          "attribut": {
            "id": 3,
            "nom": "Stockage",
            "type_valeur": "texte"
          }
        }
      ]
    }
  ],
  "produit": {
    "id": 1,
    "nom_produit": "Smartphone Galaxy S24"
  }
}
```

### **Créer une Variante**

```http
POST /api/produits/{produit_id}/variantes
```

**Paramètres :**
- `produit_id` (requis) : ID du produit parent

**Corps de la requête :**
```json
{
  "sku": "GAL-S24-256-WHT",
  "prix_supplement": 100.00,
  "stock": 15,
  "actif": true,
  "valeurs": [
    {
      "attribut_id": 2,
      "valeur": "Blanc"
    },
    {
      "attribut_id": 3,
      "valeur": "256GB"
    }
  ]
}
```

**Règles de validation :**
- `sku` : requis, chaîne, max 100 caractères, unique
- `prix_supplement` : requis, numérique, min 0
- `stock` : requis, entier, min 0
- `actif` : optionnel, booléen (défaut: true)
- `valeurs` : requis, tableau
- `valeurs.*.attribut_id` : requis, doit exister dans la table attributs
- `valeurs.*.valeur` : requis

**Exemple de requête :**
```bash
curl -X POST "https://api.example.com/api/produits/1/variantes" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "sku": "GAL-S24-256-WHT",
    "prix_supplement": 100.00,
    "stock": 15,
    "actif": true,
    "valeurs": [
      {
        "attribut_id": 2,
        "valeur": "Blanc"
      },
      {
        "attribut_id": 3,
        "valeur": "256GB"
      }
    ]
  }'
```

**Réponse (201 Created) :**
```json
{
  "message": "Variante créée avec succès",
  "variante": {
    "id": 5,
    "produit_parent_id": 1,
    "sku": "GAL-S24-256-WHT",
    "prix_supplement": "100.00",
    "stock": 15,
    "actif": true,
    "created_at": "2024-01-25T16:00:00.000000Z",
    "updated_at": "2024-01-25T16:00:00.000000Z",
    "valeurs": [
      {
        "id": 10,
        "produit_variante_id": 5,
        "attribut_id": 2,
        "valeur": "Blanc",
        "attribut": {
          "id": 2,
          "nom": "Couleur"
        }
      },
      {
        "id": 11,
        "produit_variante_id": 5,
        "attribut_id": 3,
        "valeur": "256GB",
        "attribut": {
          "id": 3,
          "nom": "Stockage"
        }
      }
    ]
  }
}
```

### **Mettre à Jour une Variante**

```http
PUT /api/variantes/{id}
```

**Paramètres :**
- `id` (requis) : ID de la variante

**Corps de la requête :**
```json
{
  "prix_supplement": 120.00,
  "stock": 20,
  "valeurs": [
    {
      "attribut_id": 2,
      "valeur": "Blanc Nacré"
    }
  ]
}
```

**Règles de validation :**
- `sku` : parfois requis, chaîne, max 100 caractères, unique
- `prix_supplement` : parfois requis, numérique, min 0
- `stock` : parfois requis, entier, min 0
- `actif` : optionnel, booléen
- `valeurs` : optionnel, tableau
- `valeurs.*.attribut_id` : requis, doit exister dans la table attributs
- `valeurs.*.valeur` : requis

**Exemple de requête :**
```bash
curl -X PUT "https://api.example.com/api/variantes/5" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "prix_supplement": 120.00,
    "stock": 20
  }'
```

**Réponse :**
```json
{
  "message": "Variante mise à jour avec succès",
  "variante": {
    "id": 5,
    "sku": "GAL-S24-256-WHT",
    "prix_supplement": "120.00",
    "stock": 20,
    "updated_at": "2024-01-25T17:30:00.000000Z"
  }
}
```

### **Supprimer une Variante**

```http
DELETE /api/variantes/{id}
```

**Paramètres :**
- `id` (requis) : ID de la variante

**Exemple de requête :**
```bash
curl -X DELETE "https://api.example.com/api/variantes/5" \
  -H "Accept: application/json"
```

**Réponse :**
```json
{
  "message": "Variante supprimée avec succès"
}
```

### **Mettre à Jour le Stock d'une Variante**

```http
PATCH /api/variantes/{id}/stock
```

**Paramètres :**
- `id` (requis) : ID de la variante

**Corps de la requête :**
```json
{
  "stock": 35
}
```

**Exemple de requête :**
```bash
curl -X PATCH "https://api.example.com/api/variantes/5/stock" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"stock": 35}'
```

**Réponse :**
```json
{
  "message": "Stock mis à jour avec succès",
  "variante": {
    "id": 5,
    "stock": 35,
    "updated_at": "2024-01-25T18:00:00.000000Z"
  }
}
```

---

## 🏷️ Gestion des Attributs

### **Lister les Attributs**

```http
GET /api/attributs
```

**Paramètres de requête :**

- `groupe_id` (optionnel) : Filtrer par groupe d'attributs
- `type_valeur` (optionnel) : Filtrer par type de valeur (texte, nombre, booleen, date)
- `filtrable` (optionnel) : `true` pour les attributs filtrables uniquement
- `comparable` (optionnel) : `true` pour les attributs comparables uniquement

**Exemple de requête :**

```bash
curl -X GET "https://api.example.com/api/attributs?filtrable=true&type_valeur=texte" \
  -H "Accept: application/json"
```

**Réponse :**

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "nom": "Couleur",
      "description": "Couleur du produit",
      "type_valeur": "texte",
      "obligatoire": false,
      "filtrable": true,
      "comparable": true,
      "created_at": "2024-01-10T09:00:00.000000Z",
      "groupe": {
        "id": 1,
        "nom": "Apparence",
        "description": "Attributs visuels du produit"
      }
    },
    {
      "id": 2,
      "nom": "Stockage",
      "description": "Capacité de stockage",
      "type_valeur": "texte",
      "obligatoire": true,
      "filtrable": true,
      "comparable": true,
      "groupe": {
        "id": 2,
        "nom": "Spécifications",
        "description": "Caractéristiques techniques"
      }
    }
  ]
}
```

### **Créer un Attribut**

```http
POST /api/attributs
```

**Corps de la requête :**

```json
{
  "nom": "Taille Écran",
  "description": "Taille de l'écran en pouces",
  "type_valeur": "nombre",
  "groupe_id": 2,
  "obligatoire": false,
  "filtrable": true,
  "comparable": true,
  "sous_categories": [
    {
      "id": 3,
      "obligatoire": true
    },
    {
      "id": 4,
      "obligatoire": false
    }
  ]
}
```

**Règles de validation :**

- `nom` : requis, chaîne, max 255 caractères, unique
- `description` : optionnel, chaîne
- `type_valeur` : requis, enum (texte, nombre, booleen, date)
- `groupe_id` : optionnel, doit exister dans la table groupe_attributs
- `obligatoire` : optionnel, booléen (défaut: false)
- `filtrable` : optionnel, booléen (défaut: false)
- `comparable` : optionnel, booléen (défaut: false)
- `sous_categories` : optionnel, tableau
- `sous_categories.*.id` : requis, doit exister dans la table sous_categories
- `sous_categories.*.obligatoire` : optionnel, booléen

**Exemple de requête :**

```bash
curl -X POST "https://api.example.com/api/attributs" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "nom": "Taille Écran",
    "description": "Taille de l'\''écran en pouces",
    "type_valeur": "nombre",
    "groupe_id": 2,
    "filtrable": true,
    "comparable": true
  }'
```

**Réponse (201 Created) :**

```json
{
  "id": 15,
  "nom": "Taille Écran",
  "description": "Taille de l'écran en pouces",
  "type_valeur": "nombre",
  "groupe_id": 2,
  "obligatoire": false,
  "filtrable": true,
  "comparable": true,
  "created_at": "2024-01-25T19:00:00.000000Z",
  "updated_at": "2024-01-25T19:00:00.000000Z",
  "groupe": {
    "id": 2,
    "nom": "Spécifications"
  },
  "sousCategories": []
}
```

### **Obtenir les Attributs Filtrables**

```http
GET /api/attributs/filtrables
```

**Exemple de requête :**

```bash
curl -X GET "https://api.example.com/api/attributs/filtrables" \
  -H "Accept: application/json"
```

**Réponse :**

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "nom": "Couleur",
      "type_valeur": "texte",
      "valeurs_possibles": [
        "Noir",
        "Blanc",
        "Rouge",
        "Bleu"
      ]
    },
    {
      "id": 2,
      "nom": "Stockage",
      "type_valeur": "texte",
      "valeurs_possibles": [
        "64GB",
        "128GB",
        "256GB",
        "512GB"
      ]
    }
  ]
}
```

### **Associer un Attribut à une Sous-Catégorie**

```http
POST /api/attributs/{id}/sous-categories/{sousCategorieId}
```

**Paramètres :**

- `id` (requis) : ID de l'attribut
- `sousCategorieId` (requis) : ID de la sous-catégorie

**Corps de la requête :**

```json
{
  "obligatoire": true
}
```

**Exemple de requête :**

```bash
curl -X POST "https://api.example.com/api/attributs/15/sous-categories/3" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"obligatoire": true}'
```

**Réponse :**

```json
{
  "message": "Attribut associé à la sous-catégorie avec succès",
  "attribut": {
    "id": 15,
    "nom": "Taille Écran",
    "sousCategories": [
      {
        "id": 3,
        "nom_sous_categorie": "Téléphones",
        "pivot": {
          "obligatoire": true
        }
      }
    ]
  }
}
```

---

## 📸 Gestion des Images

### **Télécharger une Image**

```http
POST /api/images/upload
```

**Corps de la requête (multipart/form-data) :**

- `model_type` (requis) : Type de modèle (produit, categorie, sous_categorie, sous_sous_categorie, collection, marque, produit_variante, carousel_slide)
- `model_id` (requis) : ID du modèle
- `image` (requis) : Fichier image (max 10MB)
- `is_primary` (optionnel) : `true` pour définir comme image principale
- `alt_text` (optionnel) : Texte alternatif (max 255 caractères)
- `title` (optionnel) : Titre de l'image (max 255 caractères)

**Exemple de requête :**

```bash
curl -X POST "https://api.example.com/api/images/upload" \
  -H "Accept: application/json" \
  -F "model_type=produit" \
  -F "model_id=1" \
  -F "image=@/path/to/image.jpg" \
  -F "is_primary=true" \
  -F "alt_text=Vue de face du produit" \
  -F "title=Image principale"
```

**Réponse (201 Created) :**

```json
{
  "success": true,
  "message": "Image téléchargée avec succès",
  "image": {
    "id": 25,
    "path": "produits/1/image-1706198400.jpg",
    "filename": "image-1706198400.jpg",
    "disk": "public",
    "mime_type": "image/jpeg",
    "size": 2048576,
    "alt_text": "Vue de face du produit",
    "title": "Image principale",
    "is_primary": true,
    "order": 1,
    "imageable_type": "App\\Models\\Produit",
    "imageable_id": 1,
    "created_at": "2024-01-25T20:00:00.000000Z",
    "url": "/api/images/serve/25",
    "direct_url": "/storage/produits/1/image-1706198400.jpg",
    "thumbnail_small": "/api/images/thumbnail/25/small",
    "thumbnail_medium": "/api/images/thumbnail/25/medium",
    "thumbnail_large": "/api/images/thumbnail/25/large"
  }
}
```

### **Télécharger Plusieurs Images**

```http
POST /api/images/upload-multiple
```

**Corps de la requête (multipart/form-data) :**

- `model_type` (requis) : Type de modèle
- `model_id` (requis) : ID du modèle
- `images[]` (requis) : Tableau de fichiers images
- `alt_text` (optionnel) : Texte alternatif pour toutes les images
- `title` (optionnel) : Titre pour toutes les images

**Exemple de requête :**

```bash
curl -X POST "https://api.example.com/api/images/upload-multiple" \
  -H "Accept: application/json" \
  -F "model_type=produit" \
  -F "model_id=1" \
  -F "images[]=@/path/to/image1.jpg" \
  -F "images[]=@/path/to/image2.jpg" \
  -F "images[]=@/path/to/image3.jpg" \
  -F "alt_text=Images du produit"
```

**Réponse (201 Created) :**

```json
{
  "success": true,
  "message": "3 images téléchargées avec succès",
  "images": [
    {
      "id": 26,
      "path": "produits/1/image-1706198401.jpg",
      "is_primary": false,
      "order": 2,
      "url": "/api/images/serve/26"
    },
    {
      "id": 27,
      "path": "produits/1/image-1706198402.jpg",
      "is_primary": false,
      "order": 3,
      "url": "/api/images/serve/27"
    },
    {
      "id": 28,
      "path": "produits/1/image-1706198403.jpg",
      "is_primary": false,
      "order": 4,
      "url": "/api/images/serve/28"
    }
  ]
}
```

### **Obtenir les Images d'un Modèle**

```http
GET /api/images/get
```

**Paramètres de requête :**

- `model_type` (requis) : Type de modèle
- `model_id` (requis) : ID du modèle

**Exemple de requête :**

```bash
curl -X GET "https://api.example.com/api/images/get?model_type=produit&model_id=1" \
  -H "Accept: application/json"
```

**Réponse :**

```json
{
  "success": true,
  "images": [
    {
      "id": 25,
      "path": "produits/1/image-1706198400.jpg",
      "filename": "image-1706198400.jpg",
      "alt_text": "Vue de face du produit",
      "title": "Image principale",
      "is_primary": true,
      "order": 1,
      "url": "/api/images/serve/25",
      "direct_url": "/storage/produits/1/image-1706198400.jpg",
      "thumbnail_small": "/api/images/thumbnail/25/small",
      "thumbnail_medium": "/api/images/thumbnail/25/medium",
      "thumbnail_large": "/api/images/thumbnail/25/large"
    },
    {
      "id": 26,
      "path": "produits/1/image-1706198401.jpg",
      "filename": "image-1706198401.jpg",
      "alt_text": "Images du produit",
      "is_primary": false,
      "order": 2,
      "url": "/api/images/serve/26",
      "thumbnail_small": "/api/images/thumbnail/26/small",
      "thumbnail_medium": "/api/images/thumbnail/26/medium",
      "thumbnail_large": "/api/images/thumbnail/26/large"
    }
  ],
  "model": {
    "type": "produit",
    "id": 1,
    "nom": "Smartphone Galaxy S24"
  }
}
```

### **Mettre à Jour une Image**

```http
PUT /api/images/{id}
```

**Paramètres :**

- `id` (requis) : ID de l'image

**Corps de la requête :**

```json
{
  "alt_text": "Nouveau texte alternatif",
  "title": "Nouveau titre",
  "is_primary": true,
  "order": 1
}
```

**Exemple de requête :**

```bash
curl -X PUT "https://api.example.com/api/images/25" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "alt_text": "Vue de face mise à jour",
    "title": "Image principale mise à jour",
    "is_primary": true
  }'
```

**Réponse :**

```json
{
  "success": true,
  "message": "Image mise à jour avec succès",
  "image": {
    "id": 25,
    "alt_text": "Vue de face mise à jour",
    "title": "Image principale mise à jour",
    "is_primary": true,
    "updated_at": "2024-01-25T21:00:00.000000Z"
  }
}
```

### **Supprimer une Image**

```http
DELETE /api/images/{id}
```

**Paramètres :**

- `id` (requis) : ID de l'image

**Exemple de requête :**

```bash
curl -X DELETE "https://api.example.com/api/images/25" \
  -H "Accept: application/json"
```

**Réponse :**

```json
{
  "success": true,
  "message": "Image supprimée avec succès"
}
```

### **Réorganiser les Images**

```http
POST /api/images/reorder
```

**Corps de la requête :**

```json
{
  "model_type": "produit",
  "model_id": 1,
  "image_ids": [25, 27, 26, 28]
}
```

**Exemple de requête :**

```bash
curl -X POST "https://api.example.com/api/images/reorder" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "model_type": "produit",
    "model_id": 1,
    "image_ids": [25, 27, 26, 28]
  }'
```

**Réponse :**

```json
{
  "success": true,
  "message": "Images réorganisées avec succès",
  "images": [
    {
      "id": 25,
      "order": 1
    },
    {
      "id": 27,
      "order": 2
    },
    {
      "id": 26,
      "order": 3
    },
    {
      "id": 28,
      "order": 4
    }
  ]
}
```

### **Servir une Image**

```http
GET /api/images/serve/{id}
```

**Paramètres :**

- `id` (requis) : ID de l'image

**Exemple de requête :**

```bash
curl -X GET "https://api.example.com/api/images/serve/25" \
  -H "Accept: image/*"
```

**Réponse :** Fichier image binaire

### **Obtenir une Miniature**

```http
GET /api/images/thumbnail/{id}/{size}
```

**Paramètres :**

- `id` (requis) : ID de l'image
- `size` (requis) : Taille de la miniature (small, medium, large)

**Tailles disponibles :**

- `small` : 150px
- `medium` : 300px
- `large` : 600px

**Exemple de requête :**

```bash
curl -X GET "https://api.example.com/api/images/thumbnail/25/medium" \
  -H "Accept: image/*"
```

**Réponse :** Fichier image miniature binaire

---

## 📊 Structures de Données

### **Modèle Produit**

```json
{
  "id": 1,
  "nom_produit": "Nom du produit",
  "description_produit": "Description détaillée",
  "prix_produit": "999.99",
  "quantite_produit": 50,
  "reference": "MARQUE-ID",
  "marque_id": 5,
  "sous_sous_categorie_id": 12,
  "created_at": "2024-01-15T10:30:00.000000Z",
  "updated_at": "2024-01-20T14:45:00.000000Z",
  "marque": {
    "id": 5,
    "nom_marque": "Nom de la marque"
  },
  "sousSousCategorie": {
    "id": 12,
    "nom_sous_sous_categorie": "Nom de la sous-sous-catégorie"
  },
  "images": [],
  "variantes": [],
  "valeurs": [],
  "promotions": [],
  "collections": []
}
```

### **Modèle Variante**

```json
{
  "id": 1,
  "produit_parent_id": 1,
  "sku": "PRODUIT-VARIANTE-001",
  "prix_supplement": "100.00",
  "stock": 25,
  "actif": true,
  "created_at": "2024-01-15T10:30:00.000000Z",
  "updated_at": "2024-01-20T14:45:00.000000Z",
  "valeurs": [
    {
      "id": 1,
      "attribut_id": 2,
      "valeur": "Valeur de l'attribut",
      "attribut": {
        "id": 2,
        "nom": "Nom de l'attribut",
        "type_valeur": "texte"
      }
    }
  ]
}
```

### **Modèle Attribut**

```json
{
  "id": 1,
  "nom": "Nom de l'attribut",
  "description": "Description de l'attribut",
  "type_valeur": "texte",
  "groupe_id": 1,
  "obligatoire": false,
  "filtrable": true,
  "comparable": true,
  "created_at": "2024-01-10T09:00:00.000000Z",
  "updated_at": "2024-01-15T11:20:00.000000Z",
  "groupe": {
    "id": 1,
    "nom": "Nom du groupe",
    "description": "Description du groupe"
  },
  "sousCategories": []
}
```

### **Modèle Image**

```json
{
  "id": 1,
  "path": "produits/1/image.jpg",
  "filename": "image.jpg",
  "disk": "public",
  "mime_type": "image/jpeg",
  "size": 2048576,
  "alt_text": "Texte alternatif",
  "title": "Titre de l'image",
  "is_primary": true,
  "order": 1,
  "imageable_type": "App\\Models\\Produit",
  "imageable_id": 1,
  "created_at": "2024-01-25T20:00:00.000000Z",
  "updated_at": "2024-01-25T20:00:00.000000Z",
  "url": "/api/images/serve/1",
  "direct_url": "/storage/produits/1/image.jpg",
  "thumbnail_small": "/api/images/thumbnail/1/small",
  "thumbnail_medium": "/api/images/thumbnail/1/medium",
  "thumbnail_large": "/api/images/thumbnail/1/large"
}
```

---

## ⚠️ Codes d'Erreur

### **Codes de Statut HTTP**

- **200 OK** : Requête réussie
- **201 Created** : Ressource créée avec succès
- **400 Bad Request** : Paramètres de requête invalides
- **401 Unauthorized** : Authentification requise
- **403 Forbidden** : Accès interdit
- **404 Not Found** : Ressource non trouvée
- **422 Unprocessable Entity** : Erreurs de validation
- **500 Internal Server Error** : Erreur serveur

### **Format des Erreurs de Validation**

```json
{
  "error": "Données invalides",
  "message": {
    "nom_produit": [
      "Le champ nom produit est obligatoire."
    ],
    "prix_produit": [
      "Le champ prix produit doit être un nombre.",
      "Le champ prix produit doit être supérieur ou égal à 0."
    ]
  }
}
```

### **Erreurs Courantes**

#### **Produit non trouvé**

```json
{
  "error": "Produit non trouvé",
  "message": "Le produit avec l'ID 999 n'existe pas"
}
```

#### **SKU déjà utilisé**

```json
{
  "error": "Données invalides",
  "message": {
    "sku": [
      "Le sku doit être unique."
    ]
  }
}
```

#### **Image trop volumineuse**

```json
{
  "error": "Données invalides",
  "message": {
    "image": [
      "Le fichier image ne doit pas dépasser 10240 kilobytes."
    ]
  }
}
```

#### **Type de modèle invalide**

```json
{
  "error": "Données invalides",
  "message": {
    "model_type": [
      "Le champ model type doit être l'une des valeurs suivantes : produit, categorie, sous_categorie, sous_sous_categorie, collection, marque, produit_variante, carousel_slide."
    ]
  }
}
```

---

## 🔧 Notes Techniques

### **Authentification**

Toutes les routes d'administration (POST, PUT, DELETE) nécessitent une authentification. Les routes de lecture (GET) sont publiques.

### **Limitation de Débit**

- **Routes publiques** : 60 requêtes par minute
- **Routes d'administration** : 30 requêtes par minute
- **Upload d'images** : 10 requêtes par minute

### **Formats d'Images Supportés**

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

### **Taille Maximale des Fichiers**

- **Images** : 10MB
- **Descriptions** : 5000 caractères
- **Noms** : 255 caractères

### **Cache**

Le système de cache a été désactivé pour garantir des données toujours fraîches. Toutes les requêtes accèdent directement à la base de données.

---

## 📝 Exemples d'Utilisation

### **Créer un Produit Complet avec Variantes**

1. **Créer le produit principal**
2. **Ajouter des images**
3. **Créer des variantes avec attributs**
4. **Associer à des collections/promotions**

### **Workflow de Gestion d'Images**

1. **Télécharger les images**
2. **Définir l'image principale**
3. **Réorganiser l'ordre d'affichage**
4. **Mettre à jour les métadonnées**

Cette documentation couvre l'ensemble des fonctionnalités de gestion des produits, variantes, attributs et images de l'API Laravel. Toutes les données sont servies fraîches depuis la base de données sans mise en cache.
