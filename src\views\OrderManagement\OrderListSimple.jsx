import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert
} from '@mui/material';
import { Search as SearchIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon } from '@mui/icons-material';

// Services
import { fetchOrdersFromLiveAPI } from '../../services/orderService';

const OrderListSimple = () => {
  const navigate = useNavigate();

  // State
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);

  // Load orders from API
  const loadOrders = async (page = 1, search = '') => {
    try {
      setLoading(true);
      setError('');

      const params = {
        page: page,
        per_page: 15
      };

      if (search) params.search = search;

      console.log('🔄 Loading orders with params:', params);

      const result = await fetchOrdersFromLiveAPI(params);
      console.log('✅ Orders loaded successfully:', result);

      setOrders(result.data || []);
      setCurrentPage(result.pagination.current_page || 1);
      setTotalPages(result.pagination.last_page || 1);
      setTotalOrders(result.pagination.total || 0);
    } catch (error) {
      console.error('❌ Error loading orders:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    loadOrders(currentPage, searchTerm);
  }, [currentPage]);

  // Handlers
  const handleSearch = (event) => {
    const value = event.target.value;
    setSearchTerm(value);
    setCurrentPage(1);

    // Debounce search
    const timeoutId = setTimeout(() => {
      loadOrders(1, value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  const handleViewOrder = (order) => {
    navigate(`/app/orders/${order.id}`);
  };

  const handleRefresh = () => {
    loadOrders(currentPage, searchTerm);
  };

  // Utility functions
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    if (!amount) return '0.00 DT';
    return `${parseFloat(amount).toFixed(2)} DT`;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmee':
      case 'confirmed':
        return 'success';
      case 'en_attente':
      case 'pending':
        return 'warning';
      case 'annulee':
      case 'cancelled':
        return 'error';
      case 'livree':
      case 'delivered':
        return 'info';
      default:
        return 'default';
    }
  };

  const getPaymentMethodLabel = (method) => {
    switch (method) {
      case 'stripe':
        return 'Carte bancaire';
      case 'cash_on_delivery':
        return 'Paiement à la livraison';
      case 'local_pickup':
        return 'Retrait en magasin';
      default:
        return method || 'Non spécifié';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Chargement des commandes...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={handleRefresh}>
          Réessayer
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Typography variant="h4" gutterBottom>
        📋 Liste des Commandes - API Live
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Données en temps réel depuis l'API Laravel - {totalOrders} commandes trouvées
      </Typography>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              placeholder="Rechercher par numéro, client, email..."
              value={searchTerm}
              onChange={handleSearch}
              sx={{ flex: 1 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
            />
            <Button variant="outlined" onClick={handleRefresh} disabled={loading} startIcon={<RefreshIcon />}>
              Actualiser
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>N° Commande</TableCell>
              <TableCell>Client</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Total</TableCell>
              <TableCell>Paiement</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {orders.map((order) => (
              <TableRow key={order.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold" color="primary">
                    {order.numero_commande || `CMD-${order.id}`}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ID: {order.id}
                  </Typography>
                </TableCell>

                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {order.user?.name || 'Client'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {order.user?.email || order.email_commande || 'N/A'}
                  </Typography>
                </TableCell>

                <TableCell>
                  <Typography variant="body2">{formatDate(order.created_at)}</Typography>
                </TableCell>

                <TableCell>
                  <Typography variant="body2" fontWeight="bold" color="success.main">
                    {formatCurrency(order.total_commande)}
                  </Typography>
                </TableCell>

                <TableCell>
                  <Chip label={getPaymentMethodLabel(order.methode_paiement)} size="small" variant="outlined" />
                </TableCell>

                <TableCell>
                  <Chip label={order.status_label || order.status} size="small" color={getStatusColor(order.status)} />
                </TableCell>

                <TableCell>
                  <Tooltip title="Voir les détails">
                    <IconButton size="small" onClick={() => handleViewOrder(order)} color="primary">
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination Info */}
      {orders.length > 0 && (
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Page {currentPage} sur {totalPages} - Affichage de {(currentPage - 1) * 15 + 1} à {Math.min(currentPage * 15, totalOrders)} sur{' '}
            {totalOrders} commande(s)
          </Typography>
        </Box>
      )}

      {/* Empty State */}
      {orders.length === 0 && !loading && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            Aucune commande trouvée
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {searchTerm ? 'Essayez de modifier votre recherche' : 'Aucune commande disponible'}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default OrderListSimple;
