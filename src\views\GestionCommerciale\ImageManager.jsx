import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON>, Spinner, Form, Image, Row, Col, Al<PERSON>, Card, Badge, ProgressBar } from 'react-bootstrap';
import {
  FaUpload,
  FaImage,
  FaEdit,
  FaTrash,
  FaStar,
  FaRegStar,
  FaEye,
  FaDownload,
  FaCloudUploadAlt,
  FaFileImage,
  FaCheckCircle,
  FaExclamationTriangle
} from 'react-icons/fa';
import axios from 'axios';
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';

const API_URL = 'https://laravel-api.fly.dev/api';

export default function ImageManager({ modelType, modelId, disabled }) {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [editImage, setEditImage] = useState(null);
  const [deleteImage, setDeleteImage] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const [editForm, setEditForm] = useState({ alt_text: '', title: '' });
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef();

  const fetchImages = async () => {
    if (!modelId) return;
    setLoading(true);
    setError('');
    try {
      console.log(`Fetching images for ${modelType} with ID ${modelId}`);
      const res = await axios.get(`https://laravel-api.fly.dev/api/images/get`, {
        params: { model_type: modelType, model_id: modelId },
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        }
      });
      console.log('Image fetch response:', res.data);
      setImages(res.data.images || res.data || []);
    } catch (e) {
      console.error('Error fetching images:', e);
      setError(`Erreur lors du chargement des images: ${e.message}`);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchImages();
    // eslint-disable-next-line
  }, [modelId]);

  const handleUpload = async (file) => {
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Veuillez sélectionner un fichier image valide');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('La taille du fichier ne doit pas dépasser 10MB');
      return;
    }

    const formData = new FormData();
    formData.append('image', file);
    formData.append('model_type', modelType);
    formData.append('model_id', modelId);

    setUploading(true);
    setUploadProgress(0);
    setError('');
    setSuccess('');

    try {
      console.log(`Uploading image for ${modelType} with ID ${modelId}`);
      const res = await axios.post(`https://laravel-api.fly.dev/api/images/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Accept: 'application/json'
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(progress);
        }
      });
      console.log('Upload response:', res.data);
      setSuccess('Image téléchargée avec succès');
      fetchImages();
    } catch (e) {
      console.error('Error uploading image:', e);
      setError(`Erreur lors de l'upload: ${e.message}`);
    }

    setUploading(false);
    setUploadProgress(0);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const handleFileSelect = (e) => {
    const file = e.target.files?.[0];
    if (file) handleUpload(file);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    const file = e.dataTransfer.files?.[0];
    if (file) handleUpload(file);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const confirmDelete = (image) => {
    setDeleteImage(image);
    setShowDeleteModal(true);
  };

  const handleDelete = async () => {
    if (!deleteImage) return;
    setError('');
    try {
      await axios.delete(`https://laravel-api.fly.dev/api/images/${deleteImage.id}`, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        }
      });
      setSuccess('Image supprimée avec succès');
      fetchImages();
      setShowDeleteModal(false);
      setDeleteImage(null);
    } catch (e) {
      console.error('Error deleting image:', e);
      setError(`Erreur lors de la suppression: ${e.message}`);
    }
  };

  const openPreview = (image) => {
    setPreviewImage(image);
    setShowPreviewModal(true);
  };

  const handleSetPrimary = async (id) => {
    setError('');
    try {
      await axios.put(
        `https://laravel-api.fly.dev/api/images/${id}`,
        { is_primary: true },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json'
          }
        }
      );
      fetchImages();
    } catch (e) {
      console.error('Error setting primary image:', e);
      setError(`Erreur lors de la mise à jour: ${e.message}`);
    }
  };

  const openEditModal = (img) => {
    setEditImage(img);
    setEditForm({ alt_text: img.alt_text || '', title: img.title || '' });
    setShowEditModal(true);
  };

  const handleEditChange = (e) => {
    setEditForm({ ...editForm, [e.target.name]: e.target.value });
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    if (!editImage) return;
    setError('');
    try {
      await axios.put(`https://laravel-api.fly.dev/api/images/${editImage.id}`, editForm, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        }
      });
      setShowEditModal(false);
      fetchImages();
    } catch (e) {
      console.error('Error updating image:', e);
      setError(`Erreur lors de la modification: ${e.message}`);
    }
  };

  return (
    <div className="image-manager">
      {error && (
        <Alert variant="danger" className="d-flex align-items-center">
          <FaExclamationTriangle className="me-2" />
          {error}
        </Alert>
      )}
      {success && (
        <Alert variant="success" className="d-flex align-items-center">
          <FaCheckCircle className="me-2" />
          {success}
        </Alert>
      )}

      {/* Upload Section */}
      <Card className="mb-4 border-0 shadow-sm">
        <Card.Body>
          <div className="d-flex align-items-center justify-content-between mb-3">
            <div className="d-flex align-items-center">
              <FaCloudUploadAlt className="text-primary me-2" size="1.2em" />
              <h6 className="mb-0 fw-bold">{images.length > 0 ? "Remplacer l'image" : 'Ajouter une image'}</h6>
            </div>
            {images.length > 0 && (
              <Badge bg="info">
                {images.length} image{images.length > 1 ? 's' : ''} actuelle{images.length > 1 ? 's' : ''}
              </Badge>
            )}
          </div>

          {/* Information message for existing images */}
          {images.length > 0 && (
            <Alert variant="warning" className="mb-3">
              <FaExclamationTriangle className="me-2" />
              <strong>Une image est déjà associée à cette diapositive.</strong>
              <br />
              Pour ajouter une nouvelle image, vous devez d'abord supprimer l'image existante.
            </Alert>
          )}

          {/* Drag and Drop Zone */}
          <div
            className={`upload-zone border-2 border-dashed rounded p-4 text-center position-relative ${
              images.length > 0 ? 'border-warning bg-light opacity-75' : dragOver ? 'border-primary bg-light' : 'border-secondary'
            } ${uploading ? 'uploading' : ''}`}
            onDrop={images.length === 0 ? handleDrop : undefined}
            onDragOver={images.length === 0 ? handleDragOver : undefined}
            onDragLeave={images.length === 0 ? handleDragLeave : undefined}
            style={{
              transition: 'all 0.3s ease',
              cursor: uploading || disabled || !modelId || images.length > 0 ? 'not-allowed' : 'pointer'
            }}
            onClick={() => !uploading && !disabled && modelId && images.length === 0 && fileInputRef.current?.click()}
          >
            {uploading ? (
              <div>
                <Spinner animation="border" variant="primary" className="mb-2" />
                <div className="fw-medium text-primary">Téléchargement en cours...</div>
                <ProgressBar now={uploadProgress} label={`${uploadProgress}%`} className="mt-2" style={{ height: '8px' }} />
              </div>
            ) : images.length > 0 ? (
              <div>
                <FaExclamationTriangle size="3em" className="text-warning mb-3" />
                <div className="fw-medium mb-2 text-warning">Zone d'upload désactivée</div>
                <div className="text-muted small">Supprimez l'image existante pour en ajouter une nouvelle</div>
                <Button variant="outline-warning" size="sm" className="mt-3" disabled>
                  <FaUpload className="me-2" />
                  Upload désactivé
                </Button>
              </div>
            ) : (
              <div>
                <FaFileImage size="3em" className="text-muted mb-3" />
                <div className="fw-medium mb-2">
                  {dragOver ? 'Déposez votre image ici' : 'Glissez-déposez une image ou cliquez pour sélectionner'}
                </div>
                <div className="text-muted small">Formats supportés: JPG, PNG, GIF, WebP (max 10MB)</div>
                <Button variant="primary" size="sm" className="mt-3" disabled={uploading || disabled || !modelId}>
                  <FaUpload className="me-2" />
                  Choisir un fichier
                </Button>
              </div>
            )}

            <Form.Control
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              ref={fileInputRef}
              disabled={uploading || disabled || !modelId || images.length > 0}
              style={{ display: 'none' }}
            />
          </div>
        </Card.Body>
      </Card>

      {/* Images Gallery */}
      <Card className="border-0 shadow-sm">
        <Card.Body>
          <div className="d-flex align-items-center justify-content-between mb-3">
            <div className="d-flex align-items-center">
              <FaImage className="text-primary me-2" size="1.2em" />
              <h6 className="mb-0 fw-bold">Galerie d'images</h6>
              <Badge bg="secondary" className="ms-2">
                {images.length}
              </Badge>
            </div>
          </div>

          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3 text-muted">Chargement des images...</p>
            </div>
          ) : images.length === 0 ? (
            <div className="text-center py-5">
              <FaFileImage size="3em" className="text-muted mb-3" />
              <p className="text-muted mb-0">Aucune image trouvée</p>
              <p className="text-muted small">Ajoutez votre première image ci-dessus</p>
            </div>
          ) : (
            <Row>
              {images.map((img) => (
                <Col key={img.id} xs={6} md={4} lg={3} className="mb-4">
                  <Card className="h-100 border-0 shadow-sm image-card">
                    <div className="position-relative">
                      <Image
                        src={img.thumbnail_medium || img.direct_url}
                        alt={img.alt_text}
                        title={img.title}
                        fluid
                        className="card-img-top"
                        style={{
                          height: '180px',
                          objectFit: 'cover',
                          cursor: 'pointer'
                        }}
                        onClick={() => openPreview(img)}
                      />

                      {/* Primary Badge */}
                      {img.is_primary && (
                        <Badge bg="success" className="position-absolute top-0 start-0 m-2" style={{ fontSize: '0.7em' }}>
                          <FaStar className="me-1" />
                          Principale
                        </Badge>
                      )}

                      {/* Preview Overlay */}
                      <div className="image-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                        <Button
                          variant="light"
                          size="sm"
                          className="rounded-circle"
                          onClick={(e) => {
                            e.stopPropagation();
                            openPreview(img);
                          }}
                        >
                          <FaEye />
                        </Button>
                      </div>
                    </div>

                    <Card.Body className="p-3">
                      <div className="d-flex flex-wrap gap-1 mb-2">
                        <Button size="sm" variant="outline-primary" onClick={() => openEditModal(img)} disabled={disabled} title="Éditer">
                          <FaEdit />
                        </Button>

                        {!img.is_primary && (
                          <Button
                            size="sm"
                            variant="outline-warning"
                            onClick={() => handleSetPrimary(img.id)}
                            disabled={disabled}
                            title="Définir comme image principale"
                          >
                            <FaRegStar />
                          </Button>
                        )}

                        <Button size="sm" variant="outline-danger" onClick={() => confirmDelete(img)} disabled={disabled} title="Supprimer">
                          <FaTrash />
                        </Button>
                      </div>

                      {img.alt_text && (
                        <div className="text-muted small text-truncate" title={img.alt_text}>
                          {img.alt_text}
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          )}
        </Card.Body>
      </Card>
      {/* Edit Modal */}
      <ProfessionalModal
        show={showEditModal}
        onHide={() => setShowEditModal(false)}
        title="Éditer l'image"
        subtitle="Modifier les informations de l'image"
        icon={<FaEdit />}
        size="md"
        primaryAction={handleEditSubmit}
        secondaryAction={() => setShowEditModal(false)}
        primaryText="Enregistrer"
        secondaryText="Annuler"
        variant="primary"
      >
        <Form>
          <Form.Group className="mb-3">
            <Form.Label>Texte alternatif (alt)</Form.Label>
            <Form.Control
              name="alt_text"
              value={editForm.alt_text}
              onChange={handleEditChange}
              placeholder="Description de l'image pour l'accessibilité"
            />
            <Form.Text className="text-muted">Utilisé par les lecteurs d'écran et affiché si l'image ne se charge pas</Form.Text>
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Titre</Form.Label>
            <Form.Control name="title" value={editForm.title} onChange={handleEditChange} placeholder="Titre de l'image" />
            <Form.Text className="text-muted">Affiché au survol de l'image</Form.Text>
          </Form.Group>
        </Form>
      </ProfessionalModal>

      {/* Delete Confirmation Modal */}
      <ProfessionalModal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        title="Confirmer la suppression"
        subtitle="Cette action ne peut pas être annulée"
        icon={<FaExclamationTriangle />}
        size="sm"
        primaryAction={handleDelete}
        secondaryAction={() => setShowDeleteModal(false)}
        primaryText="Supprimer"
        secondaryText="Annuler"
        primaryVariant="danger"
        variant="danger"
      >
        <div className="text-center">
          <p className="mb-0">Êtes-vous sûr de vouloir supprimer cette image ?</p>
          {deleteImage?.is_primary && (
            <div className="mt-2">
              <Badge bg="warning" className="text-dark">
                <FaStar className="me-1" />
                Cette image est définie comme image principale
              </Badge>
            </div>
          )}
        </div>
      </ProfessionalModal>

      {/* Preview Modal */}
      <ProfessionalModal
        show={showPreviewModal}
        onHide={() => setShowPreviewModal(false)}
        title={previewImage?.title || "Aperçu de l'image"}
        subtitle={previewImage?.alt_text || 'Prévisualisation'}
        icon={<FaEye />}
        size="lg"
        primaryAction={() => setShowPreviewModal(false)}
        primaryText="Fermer"
        secondaryButton={false}
        variant="info"
      >
        {previewImage && (
          <div className="text-center">
            <Image
              src={previewImage.direct_url}
              alt={previewImage.alt_text}
              fluid
              className="rounded shadow-sm"
              style={{ maxHeight: '70vh', objectFit: 'contain' }}
            />
            <div className="mt-3 d-flex justify-content-center gap-2">
              <Button variant="outline-primary" size="sm" onClick={() => window.open(previewImage.direct_url, '_blank')}>
                <FaDownload className="me-1" />
                Télécharger
              </Button>
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={() => {
                  setShowPreviewModal(false);
                  openEditModal(previewImage);
                }}
              >
                <FaEdit className="me-1" />
                Éditer
              </Button>
              <Button
                variant="outline-danger"
                size="sm"
                onClick={() => {
                  setShowPreviewModal(false);
                  confirmDelete(previewImage);
                }}
              >
                <FaTrash className="me-1" />
                Supprimer
              </Button>
            </div>
            {previewImage.is_primary && (
              <Badge bg="success" className="mt-2">
                <FaStar className="me-1" />
                Image principale
              </Badge>
            )}
          </div>
        )}
      </ProfessionalModal>

      {/* Custom CSS for enhanced styling */}
      <style jsx="true">{`
        .upload-zone {
          transition: all 0.3s ease;
          min-height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .upload-zone:hover:not(.uploading) {
          border-color: #2196f3 !important;
          background-color: #f8f9fa;
        }

        .image-card {
          transition:
            transform 0.2s ease,
            box-shadow 0.2s ease;
        }

        .image-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
        }

        .image-overlay {
          background: rgba(0, 0, 0, 0.5);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .image-card:hover .image-overlay {
          opacity: 1;
        }

        .card-img-top {
          transition: transform 0.3s ease;
        }

        .image-card:hover .card-img-top {
          transform: scale(1.05);
        }
      `}</style>
    </div>
  );
}
