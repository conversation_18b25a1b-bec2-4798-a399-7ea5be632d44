const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Products (Produits)
// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('access_token');
  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

export async function fetchProducts(params = {}) {
  try {
    const queryParams = new URLSearchParams();

    // Standard parameters from documentation
    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    if (params.search) queryParams.append('search', params.search);
    if (params.category_id) queryParams.append('category_id', params.category_id);
    if (params.marque_id) queryParams.append('marque_id', params.marque_id);
    if (params.sous_categorie_id) queryParams.append('sous_categorie_id', params.sous_categorie_id);
    if (params.sous_sous_categorie_id) queryParams.append('sous_sous_categorie_id', params.sous_sous_categorie_id);
    if (params.prix_min) queryParams.append('prix_min', params.prix_min);
    if (params.prix_max) queryParams.append('prix_max', params.prix_max);
    if (params.en_stock !== undefined) queryParams.append('en_stock', params.en_stock);
    if (params.with) queryParams.append('with', params.with);

    const url = `${API_URL}/produits${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    console.log('Fetching products from:', url);

    const res = await fetch(url, {
      headers: getAuthHeaders()
    });

    if (!res.ok) {
      throw new Error(`Failed to fetch products. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log('API returned products response:', response);

    return response;
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
}

export async function fetchProductById(id, params = {}) {
  try {
    const queryParams = new URLSearchParams();

    // Support for including relations
    if (params.with) queryParams.append('with', params.with);

    const url = `${API_URL}/produits/${id}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    const res = await fetch(url, {
      headers: getAuthHeaders()
    });

    if (!res.ok) {
      throw new Error(`Failed to fetch product ${id}. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log(`API returned product ${id} response:`, response);

    return response;
  } catch (error) {
    console.error(`Error fetching product ${id}:`, error);
    throw error;
  }
}

export async function createProduct(data) {
  try {
    const res = await fetch(`${API_URL}/produits`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de la création du produit');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error creating product:', error);
    throw error;
  }
}

export async function updateProduct(id, data) {
  try {
    console.log(`🔄 Attempting to update product with ID: ${id}`);
    const url = `${API_URL}/produits/${id}`;
    console.log(`🌐 PUT request to: ${url}`);
    console.log(`📝 Data to send:`, data);

    const res = await fetch(url, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });

    console.log(`📡 Response status: ${res.status}`);

    if (!res.ok) {
      let errorData;
      try {
        errorData = await res.json();
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
        throw new Error(`Erreur HTTP ${res.status}: ${res.statusText}`);
      }
      console.error('❌ Update failed with error:', errorData);
      throw new Error(errorData.message || `Erreur lors de la mise à jour du produit (${res.status})`);
    }

    const response = await res.json();
    console.log('✅ Product updated successfully:', response);
    return response;
  } catch (error) {
    console.error('❌ Error updating product:', error);
    throw error;
  }
}

export async function deleteProduct(id) {
  try {
    console.log(`🗑️ Starting delete for product ID: ${id}`);

    // Get authentication token
    const token = localStorage.getItem('access_token');
    console.log(`🔑 Token available:`, !!token);
    console.log(`🔑 Token preview:`, token ? `${token.substring(0, 50)}...` : 'No token');

    const headers = getAuthHeaders();
    console.log(`🔑 Auth headers:`, headers);

    const url = `${API_URL}/produits/${id}`;
    console.log(`🌐 DELETE request to: ${url}`);

    // First, let's try to fetch the product to see if it exists
    console.log(`🔍 First checking if product exists...`);
    try {
      const checkRes = await fetch(`${API_URL}/produits/${id}`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      console.log(`🔍 Product check status: ${checkRes.status}`);
      if (checkRes.ok) {
        const productData = await checkRes.json();
        console.log(`✅ Product exists:`, productData);
      } else {
        console.log(`❌ Product check failed: ${checkRes.status} ${checkRes.statusText}`);
      }
    } catch (checkError) {
      console.log(`❌ Product check error:`, checkError);
    }

    // Now try the delete - try both with and without auth headers
    console.log(`🔄 Trying delete with auth headers first...`);
    let res = await fetch(url, {
      method: 'DELETE',
      headers
    });

    // If auth headers fail with 401/403, try without auth headers like brandService
    if (!res.ok && (res.status === 401 || res.status === 403)) {
      console.log(`🔄 Auth failed, trying without auth headers like brandService...`);
      res = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
    }

    console.log(`📡 Delete response status: ${res.status}`);
    console.log(`📡 Delete response statusText: ${res.statusText}`);
    console.log(`📡 Delete response headers:`, Object.fromEntries(res.headers.entries()));

    if (!res.ok) {
      console.error(`❌ Delete failed with status: ${res.status}`);

      // Get the full error response
      const errorText = await res.text();
      console.error(`❌ Error response text:`, errorText);

      // Handle specific error cases
      if (res.status === 404) {
        console.warn(`⚠️ Product ${id} not found (404)`);
        throw new Error('Ce produit n\'existe pas ou a déjà été supprimé');
      }

      if (res.status === 401) {
        console.error(`🔒 Unauthorized (401) - Check authentication token`);
        throw new Error('Non autorisé - Veuillez vous reconnecter');
      }

      if (res.status === 403) {
        console.error(`🚫 Forbidden (403) - Insufficient permissions`);
        throw new Error('Accès refusé - Permissions insuffisantes');
      }

      // Try to parse error message
      let errorMessage = 'Erreur lors de la suppression du produit';
      try {
        const errorData = JSON.parse(errorText);
        console.error(`❌ Parsed error data:`, errorData);
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch (parseError) {
        console.error(`❌ Could not parse error response:`, parseError);
        errorMessage = `${errorMessage} (${res.status}: ${res.statusText})`;
      }
      throw new Error(errorMessage);
    }

    console.log(`✅ Delete request successful`);

    // Parse the response like brandService does
    const result = await res.json();
    console.log('📋 Delete response:', result);

    // Check if the response contains an error even with 200 status (like brandService)
    if (result.error) {
      console.error('❌ Server returned error:', result.error);

      // Handle foreign key constraint violation
      if (result.error.includes('Foreign key violation') || result.error.includes('SQLSTATE[23503]')) {
        throw new Error('Impossible de supprimer ce produit car il est référencé dans des commandes. Veuillez d\'abord supprimer les commandes associées.');
      }

      throw new Error(result.error || 'Erreur lors de la suppression du produit');
    }

    console.log('✅ Product deleted successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Error deleting product:', error);
    console.error('❌ Error name:', error.name);
    console.error('❌ Error message:', error.message);
    throw error;
  }
}

export async function searchProducts(searchTerm) {
  try {
    const res = await fetch(`${API_URL}/produits/search?q=${encodeURIComponent(searchTerm)}`, {
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error('Erreur lors de la recherche de produits');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error searching products:', error);
    throw error;
  }
}

export async function fetchProductsPaginated(page = 1, perPage = 10) {
  try {
    const res = await fetch(`${API_URL}/produits/perpages?page=${page}&per_page=${perPage}`, {
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error('Erreur lors du chargement des produits paginés');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error fetching paginated products:', error);
    throw error;
  }
}

export async function filterProducts(filters) {
  try {
    const queryParams = new URLSearchParams();

    Object.keys(filters).forEach(key => {
      if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
        queryParams.append(key, filters[key]);
      }
    });

    const res = await fetch(`${API_URL}/produits/filtrer?${queryParams.toString()}`, {
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error('Erreur lors du filtrage des produits');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error filtering products:', error);
    throw error;
  }
}

// Product Promotions
export async function addPromotionToProduct(productId, promotionId) {
  try {
    const res = await fetch(`${API_URL}/produits/${productId}/promotions`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ promotion_id: promotionId })
    });

    if (!res.ok) {
      throw new Error('Erreur lors de l\'association de la promotion au produit');
    }

    return res.json();
  } catch (error) {
    console.error('Error adding promotion to product:', error);
    throw error;
  }
}

export async function removePromotionFromProduct(productId, promotionId) {
  try {
    const res = await fetch(`${API_URL}/produits/${productId}/promotions/${promotionId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!res.ok) {
      throw new Error('Erreur lors de la dissociation de la promotion du produit');
    }

    return res.json();
  } catch (error) {
    console.error('Error removing promotion from product:', error);
    throw error;
  }
}

// Product Attributes (from documentation)
export async function fetchProductAttributes(productId) {
  try {
    const res = await fetch(`${API_URL}/produits/${productId}/attributs`, {
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error('Erreur lors du chargement des attributs du produit');
    }
    return res.json();
  } catch (error) {
    console.error('Error fetching product attributes:', error);
    throw error;
  }
}

export async function setProductAttributes(productId, attributes) {
  try {
    const res = await fetch(`${API_URL}/produits/${productId}/attributs`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ attributs: attributes })
    });
    if (!res.ok) {
      throw new Error('Erreur lors de la définition des attributs du produit');
    }
    return res.json();
  } catch (error) {
    console.error('Error setting product attributes:', error);
    throw error;
  }
}

export async function updateProductAttribute(productId, attributeId, value) {
  try {
    const res = await fetch(`${API_URL}/produits/${productId}/attributs/${attributeId}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({ valeur: value })
    });
    if (!res.ok) {
      throw new Error('Erreur lors de la mise à jour de l\'attribut du produit');
    }
    return res.json();
  } catch (error) {
    console.error('Error updating product attribute:', error);
    throw error;
  }
}

export async function removeProductAttribute(productId, attributeId) {
  try {
    const res = await fetch(`${API_URL}/produits/${productId}/attributs/${attributeId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error('Erreur lors de la suppression de l\'attribut du produit');
    }
    return res.json();
  } catch (error) {
    console.error('Error removing product attribute:', error);
    throw error;
  }
}

// Product Variants (from documentation)
export async function fetchProductVariants(productId) {
  try {
    const res = await fetch(`${API_URL}/produits/${productId}/variantes`, {
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error('Erreur lors du chargement des variantes du produit');
    }
    return res.json();
  } catch (error) {
    console.error('Error fetching product variants:', error);
    throw error;
  }
}

export async function createProductVariant(productId, variantData) {
  try {
    const res = await fetch(`${API_URL}/produits/${productId}/variantes`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(variantData)
    });
    if (!res.ok) {
      throw new Error('Erreur lors de la création de la variante');
    }
    return res.json();
  } catch (error) {
    console.error('Error creating product variant:', error);
    throw error;
  }
}

export async function fetchVariantById(variantId) {
  try {
    const res = await fetch(`${API_URL}/variantes/${variantId}`, {
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error('Erreur lors du chargement de la variante');
    }
    return res.json();
  } catch (error) {
    console.error('Error fetching variant:', error);
    throw error;
  }
}

export async function updateVariant(variantId, variantData) {
  try {
    const res = await fetch(`${API_URL}/variantes/${variantId}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(variantData)
    });
    if (!res.ok) {
      throw new Error('Erreur lors de la mise à jour de la variante');
    }
    return res.json();
  } catch (error) {
    console.error('Error updating variant:', error);
    throw error;
  }
}

export async function deleteVariant(variantId) {
  try {
    const res = await fetch(`${API_URL}/variantes/${variantId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error('Erreur lors de la suppression de la variante');
    }
    return res.json();
  } catch (error) {
    console.error('Error deleting variant:', error);
    throw error;
  }
}

export async function updateVariantStock(variantId, stock) {
  try {
    const res = await fetch(`${API_URL}/variantes/${variantId}/stock`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify({ stock })
    });
    if (!res.ok) {
      throw new Error('Erreur lors de la mise à jour du stock de la variante');
    }
    return res.json();
  } catch (error) {
    console.error('Error updating variant stock:', error);
    throw error;
  }
}

// Additional utility functions
export async function fetchFilterableAttributes() {
  try {
    const res = await fetch(`${API_URL}/attributs/filtrables`, {
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error('Erreur lors du chargement des attributs filtrables');
    }
    return res.json();
  } catch (error) {
    console.error('Error fetching filterable attributes:', error);
    throw error;
  }
}
