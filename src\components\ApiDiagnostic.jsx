import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { runApiDiagnostic, generateDiagnosticReport, getRecommendations } from '../utils/apiDiagnostic';

const ApiDiagnostic = ({ onClose }) => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [expanded, setExpanded] = useState(false);

  const runDiagnostic = async () => {
    setLoading(true);
    try {
      const diagnosticResults = await runApiDiagnostic();
      setResults(diagnosticResults);
      setExpanded(true);
    } catch (error) {
      console.error('Erreur lors du diagnostic:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'PASS':
        return <CheckCircleIcon sx={{ color: 'success.main' }} />;
      case 'FAIL':
        return <ErrorIcon sx={{ color: 'error.main' }} />;
      case 'SKIP':
        return <WarningIcon sx={{ color: 'warning.main' }} />;
      default:
        return <InfoIcon sx={{ color: 'info.main' }} />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'PASS':
        return 'success';
      case 'FAIL':
        return 'error';
      case 'SKIP':
        return 'warning';
      default:
        return 'default';
    }
  };

  const copyReport = () => {
    if (results) {
      const report = generateDiagnosticReport(results);
      navigator.clipboard.writeText(report);
      alert('Rapport copié dans le presse-papiers');
    }
  };

  return (
    <Card sx={{ maxWidth: 800, mx: 'auto', mt: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5" component="h2">
            🔍 Diagnostic API
          </Typography>
          {onClose && (
            <Button onClick={onClose} variant="outlined" size="small">
              Fermer
            </Button>
          )}
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Cet outil diagnostique les problèmes de connexion avec l'API pour identifier la cause de l'erreur.
        </Typography>

        {!results && (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Button
              variant="contained"
              onClick={runDiagnostic}
              disabled={loading}
              size="large"
              sx={{ minWidth: 200 }}
            >
              {loading ? (
                <>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Diagnostic en cours...
                </>
              ) : (
                'Lancer le diagnostic'
              )}
            </Button>
          </Box>
        )}

        {results && (
          <Box>
            {/* Résumé */}
            <Alert 
              severity={
                results.tests.some(t => t.status === 'FAIL') ? 'error' : 
                results.tests.some(t => t.status === 'SKIP') ? 'warning' : 'success'
              }
              sx={{ mb: 2 }}
            >
              <Typography variant="h6" gutterBottom>
                Résumé du diagnostic
              </Typography>
              <Typography variant="body2">
                ✅ Tests réussis: {results.tests.filter(t => t.status === 'PASS').length} | 
                ❌ Tests échoués: {results.tests.filter(t => t.status === 'FAIL').length} | 
                ⏭️ Tests ignorés: {results.tests.filter(t => t.status === 'SKIP').length}
              </Typography>
            </Alert>

            {/* Recommandations */}
            {getRecommendations(results).length > 0 && (
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  🎯 Recommandations
                </Typography>
                <List dense>
                  {getRecommendations(results).map((rec, index) => (
                    <ListItem key={index} sx={{ py: 0 }}>
                      <ListItemText primary={rec} />
                    </ListItem>
                  ))}
                </List>
              </Alert>
            )}

            {/* Détails des tests */}
            <Accordion expanded={expanded} onChange={() => setExpanded(!expanded)}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Détails des tests</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Environnement:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    <Chip label={`API: ${results.environment.apiUrl}`} size="small" />
                    <Chip 
                      label={results.environment.hasToken ? 'Token: ✅' : 'Token: ❌'} 
                      size="small" 
                      color={results.environment.hasToken ? 'success' : 'error'}
                    />
                    <Chip 
                      label={results.environment.online ? 'En ligne: ✅' : 'Hors ligne: ❌'} 
                      size="small" 
                      color={results.environment.online ? 'success' : 'error'}
                    />
                  </Box>
                </Box>

                {results.tests.map((test, index) => (
                  <Box key={index} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      {getStatusIcon(test.status)}
                      <Typography variant="subtitle1" sx={{ ml: 1, mr: 2 }}>
                        {test.name}
                      </Typography>
                      <Chip label={test.status} color={getStatusColor(test.status)} size="small" />
                    </Box>
                    
                    {test.error && (
                      <Alert severity="error" sx={{ mb: 1 }}>
                        <Typography variant="body2">
                          <strong>Erreur:</strong> {test.error}
                        </Typography>
                      </Alert>
                    )}
                    
                    {test.details && (
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Détails:</strong>
                        </Typography>
                        <Box component="pre" sx={{ 
                          fontSize: '0.75rem', 
                          backgroundColor: 'grey.100', 
                          p: 1, 
                          borderRadius: 1,
                          overflow: 'auto',
                          maxHeight: 200
                        }}>
                          {JSON.stringify(test.details, null, 2)}
                        </Box>
                      </Box>
                    )}
                  </Box>
                ))}
              </AccordionDetails>
            </Accordion>

            {/* Actions */}
            <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
              <Button variant="outlined" onClick={runDiagnostic} disabled={loading}>
                Relancer le diagnostic
              </Button>
              <Button variant="outlined" onClick={copyReport}>
                Copier le rapport
              </Button>
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default ApiDiagnostic;
