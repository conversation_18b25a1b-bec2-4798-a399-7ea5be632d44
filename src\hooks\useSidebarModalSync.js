import { useEffect } from 'react';
import { useGetMenuMaster } from 'api/menu';

/**
 * Hook to synchronize modal positioning with sidebar state
 * Updates CSS custom properties to ensure modals are properly positioned
 * based on whether the sidebar is open or closed
 */
export const useSidebarModalSync = () => {
  const { menuMaster } = useGetMenuMaster();
  const drawerOpen = menuMaster?.isDashboardDrawerOpened;

  useEffect(() => {
    // Update CSS custom properties based on sidebar state
    const updateModalPositioning = () => {
      const root = document.documentElement;
      
      if (drawerOpen) {
        // Sidebar is open (260px width)
        root.classList.add('sidebar-open');
        root.classList.remove('sidebar-closed');
        root.style.setProperty('--current-sidebar-width', '260px');
      } else {
        // Sidebar is closed/mini (72px width)
        root.classList.add('sidebar-closed');
        root.classList.remove('sidebar-open');
        root.style.setProperty('--current-sidebar-width', '72px');
      }
    };

    updateModalPositioning();
  }, [drawerOpen]);

  return { drawerOpen };
};

export default useSidebarModalSync;
