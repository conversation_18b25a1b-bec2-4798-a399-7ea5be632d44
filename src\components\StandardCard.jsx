import React from 'react';
import { <PERSON>, CardContent, CardHeader, Typography, Box, Divider } from '@mui/material';
import { COLORS, TYPOGRAPHY, SPACING, CARD_STYLES, SHADOWS } from '../themes/designSystem';

/**
 * Standardized Card Component
 * Provides consistent card styling across the application
 *
 * @param {Object} props
 * @param {string} props.title - Card title
 * @param {React.ReactNode} props.subtitle - Card subtitle
 * @param {React.ReactNode} props.action - Action component in header
 * @param {React.ReactNode} props.children - Card content
 * @param {boolean} props.divider - Show divider after header
 * @param {'small'|'medium'|'large'} props.size - Card size
 * @param {boolean} props.shadow - Enable shadow
 * @param {Object} props.sx - Additional styling
 */
const StandardCard = ({ title, subtitle, action, children, divider = true, size = 'medium', shadow = true, sx = {}, ...props }) => {
  // Size configurations
  const sizeConfig = {
    small: {
      padding: SPACING.md
    },
    medium: {
      padding: SPACING.lg
    },
    large: {
      padding: SPACING.xl
    }
  };

  const currentSizeConfig = sizeConfig[size];

  return (
    <Card
      sx={{
        backgroundColor: COLORS.background.paper,
        boxShadow: shadow ? '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)' : 'none',
        border: `1px solid ${COLORS.grey[200]}`,
        borderRadius: '16px',
        background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: `linear-gradient(90deg, ${COLORS.primary.main}, ${COLORS.secondary.main}, ${COLORS.success.main})`,
          opacity: 0.8
        },
        '&:hover': shadow
          ? {
              transform: 'translateY(-4px)',
              boxShadow: '0 20px 40px -10px rgba(0, 0, 0, 0.15)'
            }
          : {},
        ...sx
      }}
      {...props}
    >
      {(title || subtitle || action) && (
        <>
          <CardHeader
            title={
              title && (
                <Typography
                  variant="h5"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontSize: TYPOGRAPHY.fontSize.lg,
                    fontWeight: TYPOGRAPHY.fontWeight.semibold,
                    color: COLORS.text.dark,
                    margin: 0
                  }}
                >
                  {title}
                </Typography>
              )
            }
            subheader={
              subtitle && (
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontSize: TYPOGRAPHY.fontSize.sm,
                    color: COLORS.text.secondary,
                    marginTop: SPACING.xs
                  }}
                >
                  {subtitle}
                </Typography>
              )
            }
            action={action}
            sx={{
              padding: currentSizeConfig.padding,
              paddingBottom: divider ? SPACING.md : currentSizeConfig.padding,
              '& .MuiCardHeader-content': {
                overflow: 'visible'
              }
            }}
          />
          {divider && <Divider sx={{ borderColor: COLORS.grey[200] }} />}
        </>
      )}

      <CardContent
        sx={{
          padding: currentSizeConfig.padding,
          paddingTop: (title || subtitle || action) && !divider ? 0 : currentSizeConfig.padding,
          '&:last-child': {
            paddingBottom: currentSizeConfig.padding
          }
        }}
      >
        {children}
      </CardContent>
    </Card>
  );
};

export default StandardCard;
