// assets
import {
  IconPackage,
  IconShoppingCart,
  IconTags,
  IconDiscount,
  IconCategory,
  IconListDetails,
  IconPlus,
  IconCalendarEvent,
  IconBrandApple
} from '@tabler/icons-react';

// constant
const icons = {
  IconPackage,
  IconShoppingCart,
  IconTags,
  IconDiscount,
  IconCategory,
  IconListDetails,
  IconPlus,
  IconCalendarEvent,
  IconBrandApple
};

// ==============================|| PRODUCT MANAGEMENT MENU ITEMS ||============================== //

const productManagement = {
  id: 'product-management',
  title: 'Gestion des Produits',
  type: 'group',
  children: [
    {
      id: 'products',
      title: 'Produits',
      type: 'item',
      url: '/app/products',
      icon: icons.IconPackage,
      breadcrumbs: false
    },
    {
      id: 'categories',
      title: 'Catégories',
      type: 'item',
      url: '/app/categories',
      icon: icons.IconCategory,
      breadcrumbs: false
    },
    {
      id: 'attributes',
      title: 'Attributs',
      type: 'item',
      url: '/app/attributes',
      icon: icons.IconListDetails,
      breadcrumbs: false
    },
    {
      id: 'brands',
      title: 'Marques',
      type: 'item',
      url: '/app/brands',
      icon: icons.IconBrandApple,
      breadcrumbs: false
    },
    {
      id: 'collections',
      title: 'Collections',
      type: 'item',
      url: '/app/LCollection',
      icon: icons.IconTags,
      breadcrumbs: false
    },
    {
      id: 'promotions',
      title: 'Promotions',
      type: 'collapse',
      icon: icons.IconDiscount,
      children: [
        {
          id: 'promotions-list',
          title: 'Liste des Promotions',
          type: 'item',
          url: '/app/Lpromotions',
          breadcrumbs: false
        },
        {
          id: 'promotions-add',
          title: 'Ajouter une Promotion',
          type: 'item',
          url: '/app/Promotion',
          breadcrumbs: false
        },
        {
          id: 'promotions-events',
          title: 'Événements Promotionnels',
          type: 'item',
          url: '/app/PromotionEvent',
          breadcrumbs: false
        }
      ]
    }
  ]
};

export default productManagement;
