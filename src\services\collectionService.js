const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Collections
export async function fetchCollections(params = {}) {
  try {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    if (params.search) queryParams.append('search', params.search);
    if (params.active) queryParams.append('active', params.active);

    // Add with_products parameter to include product counts
    queryParams.append('with_products_count', 'true');

    const url = `${API_URL}/collections${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    console.log('Fetching collections from:', url);

    const res = await fetch(url);
    if (!res.ok) {
      throw new Error(`Failed to fetch collections. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log('API returned collections response:', response);

    return response;
  } catch (error) {
    console.error('Error fetching collections:', error);
    throw error;
  }
}

export async function fetchCollectionById(id) {
  try {
    const res = await fetch(`${API_URL}/collections/${id}`);
    if (!res.ok) {
      throw new Error(`Failed to fetch collection ${id}. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log(`API returned collection ${id} response:`, response);

    return response;
  } catch (error) {
    console.error(`Error fetching collection ${id}:`, error);
    throw error;
  }
}

export async function createCollection(data) {
  try {
    const res = await fetch(`${API_URL}/collections`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de la création de la collection');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error creating collection:', error);
    throw error;
  }
}

export async function updateCollection(id, data) {
  try {
    const res = await fetch(`${API_URL}/collections/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de la mise à jour de la collection');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error updating collection:', error);
    throw error;
  }
}

export async function deleteCollection(id) {
  try {
    const res = await fetch(`${API_URL}/collections/${id}`, {
      method: 'DELETE'
    });

    if (!res.ok) {
      throw new Error('Erreur lors de la suppression de la collection');
    }

    return res.json();
  } catch (error) {
    console.error('Error deleting collection:', error);
    throw error;
  }
}

// Collection Products
export async function fetchCollectionProducts(collectionId) {
  try {
    const res = await fetch(`${API_URL}/collections/${collectionId}/produits`);
    if (!res.ok) {
      throw new Error('Erreur lors du chargement des produits de la collection');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error fetching collection products:', error);
    throw error;
  }
}

export async function addProductsToCollection(collectionId, products) {
  try {
    const res = await fetch(`${API_URL}/collections/${collectionId}/produits`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ produits: products })
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de l\'ajout des produits à la collection');
    }

    return res.json();
  } catch (error) {
    console.error('Error adding products to collection:', error);
    throw error;
  }
}

export async function removeProductFromCollection(collectionId, productId) {
  try {
    const res = await fetch(`${API_URL}/collections/${collectionId}/produits/${productId}`, {
      method: 'DELETE'
    });

    if (!res.ok) {
      throw new Error('Erreur lors de la suppression du produit de la collection');
    }

    return res.json();
  } catch (error) {
    console.error('Error removing product from collection:', error);
    throw error;
  }
}

export async function fetchFeaturedCollectionProducts(collectionId) {
  try {
    const res = await fetch(`${API_URL}/collections/${collectionId}/produits-featured`);
    if (!res.ok) {
      throw new Error('Erreur lors du chargement des produits mis en avant de la collection');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error fetching featured collection products:', error);
    throw error;
  }
}

// Collection Promotions
export async function addPromotionToCollection(collectionId, promotionId) {
  try {
    const res = await fetch(`${API_URL}/collections/${collectionId}/promotions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ promotion_id: promotionId })
    });

    if (!res.ok) {
      throw new Error('Erreur lors de l\'association de la promotion à la collection');
    }

    return res.json();
  } catch (error) {
    console.error('Error adding promotion to collection:', error);
    throw error;
  }
}

export async function removePromotionFromCollection(collectionId, promotionId) {
  try {
    const res = await fetch(`${API_URL}/collections/${collectionId}/promotions/${promotionId}`, {
      method: 'DELETE'
    });

    if (!res.ok) {
      throw new Error('Erreur lors de la dissociation de la promotion de la collection');
    }

    return res.json();
  } catch (error) {
    console.error('Error removing promotion from collection:', error);
    throw error;
  }
}
