import React, { useState, useEffect } from 'react';
import { Box, Typography, Alert, CircularProgress, Chip } from '@mui/material';
import { Form, Row, Col } from 'react-bootstrap';
import { FaEdit, FaTrash, FaPlus, Fa<PERSON>heck, FaImage } from 'react-icons/fa';
import { fetchBrands, createBrand, updateBrand, deleteBrand } from '../../services/brandService';
import ImageManager from './ImageManager';

// Design system components
import MainCard from 'ui-component/cards/MainCard';
import StandardButton from 'ui-component/buttons/StandardButton';
import StandardTable from 'ui-component/tables/StandardTable';
import ProfessionalModal from 'ui-component/extended/ProfessionalModal';

// Design system
import { COLORS, TYPOGRAPHY } from 'themes/designSystem';

const BrandManagement = () => {
  // Helper function to convert actif field to boolean
  const convertActifToBoolean = (actif) => {
    // Handle different possible formats from API
    if (actif === null || actif === undefined) return false;
    if (typeof actif === 'boolean') return actif;
    if (typeof actif === 'string') {
      const lowerActif = actif.toLowerCase().trim();
      return lowerActif === '1' || lowerActif === 'true' || lowerActif === 'active' || lowerActif === 'yes';
    }
    if (typeof actif === 'number') return actif === 1;
    // Handle objects that might have a value property
    if (typeof actif === 'object' && actif.hasOwnProperty('value')) {
      return convertActifToBoolean(actif.value);
    }
    return false; // Default to false for any other case
  };

  // États
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [currentBrand, setCurrentBrand] = useState(null);
  const [modalAction, setModalAction] = useState('create'); // 'create' or 'edit'
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [brandToDelete, setBrandToDelete] = useState(null);
  const [formData, setFormData] = useState({
    nom_marque: '',
    description_marque: '',
    logo_marque: '',
    site_web: '',
    actif: true
  });

  // Charger les marques
  useEffect(() => {
    const loadBrands = async () => {
      try {
        setLoading(true);
        const data = await fetchBrands();
        const brandsData = data.data || data;

        if (Array.isArray(brandsData)) {
          const normalizedBrands = brandsData.map((brand) => {
            const actifValue =
              brand.actif !== undefined
                ? brand.actif
                : brand.active !== undefined
                  ? brand.active
                  : brand.is_active !== undefined
                    ? brand.is_active
                    : brand.status !== undefined
                      ? brand.status
                      : true;

            return {
              ...brand,
              actif: convertActifToBoolean(actifValue)
            };
          });

          setBrands(normalizedBrands);
        } else {
          setBrands([]);
        }
      } catch (err) {
        setError(err.message || 'Erreur lors du chargement des marques');
      } finally {
        setLoading(false);
      }
    };

    loadBrands();
  }, []);

  // Gestion des changements du formulaire
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Ouvrir le modal pour créer une nouvelle marque
  const handleCreate = () => {
    setModalAction('create');
    setCurrentBrand(null);
    setFormData({
      nom_marque: '',
      description_marque: '',
      logo_marque: '',
      site_web: '',
      actif: true
    });
    setShowModal(true);
  };

  // Ouvrir le modal pour modifier une marque
  const handleEdit = (brand) => {
    setModalAction('edit');
    setCurrentBrand(brand);
    const normalizedActif = convertActifToBoolean(brand.actif);

    setFormData({
      nom_marque: brand.nom_marque || '',
      description_marque: brand.description_marque || '',
      logo_marque: brand.logo_marque || '',
      site_web: brand.site_web || '',
      actif: normalizedActif
    });
    setShowModal(true);
  };

  // Ouvrir le modal de confirmation de suppression
  const handleDelete = (id) => {
    const brand = brands.find((b) => b.id === id);
    setBrandToDelete(brand);
    setShowDeleteModal(true);
  };

  // Confirmer la suppression
  const confirmDelete = async () => {
    if (!brandToDelete) return;

    try {
      setLoading(true);
      setError(null);

      // Call the API to delete the brand
      await deleteBrand(brandToDelete.id);

      // Refresh the brands list from the server to ensure consistency
      const updatedData = await fetchBrands();
      const brandsData = updatedData.data || updatedData;
      setBrands(Array.isArray(brandsData) ? brandsData : []);

      setSuccess('Marque supprimée avec succès');
      setShowDeleteModal(false);
      setBrandToDelete(null);
    } catch (err) {
      setError(err.message || 'Erreur lors de la suppression de la marque');
    } finally {
      setLoading(false);
    }
  };

  // Soumettre le formulaire (création ou modification)
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      // Clean up the payload
      const payload = {
        nom_marque: formData.nom_marque.trim(),
        description_marque: formData.description_marque?.trim() || null,
        logo_marque: formData.logo_marque?.trim() || null,
        site_web: formData.site_web?.trim() || null,
        actif: Boolean(formData.actif)
      };

      if (modalAction === 'create') {
        await createBrand(payload);
      } else {
        await updateBrand(currentBrand.id, payload);
      }

      setSuccess(`Marque ${modalAction === 'create' ? 'créée' : 'modifiée'} avec succès!`);

      // Mettre à jour la liste des marques
      const updatedData = await fetchBrands();
      const brandsData = updatedData.data || updatedData;
      setBrands(Array.isArray(brandsData) ? brandsData : []);

      setShowModal(false);
    } catch (err) {
      setError(err.message || "Erreur lors de l'opération");
    } finally {
      setLoading(false);
    }
  };

  // Configuration des colonnes pour StandardTable
  const columns = [
    { id: 'id', label: 'ID', width: 80, align: 'center' },
    { id: 'logo', label: 'Logo', width: 100, align: 'center' },
    { id: 'nom_marque', label: 'Nom', minWidth: 150 },
    { id: 'description_marque', label: 'Description', minWidth: 200 },
    { id: 'actif', label: 'Statut', width: 120, align: 'center' },
    { id: 'actions', label: 'Actions', width: 150, align: 'center' }
  ];

  // Fonction de rendu personnalisé pour les cellules
  const renderCell = (column, row) => {
    switch (column.id) {
      case 'logo':
        return row.logo_marque ? (
          <img
            src={row.logo_marque}
            alt={row.nom_marque}
            style={{ width: '40px', height: '40px', objectFit: 'contain', borderRadius: '4px' }}
          />
        ) : (
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 1,
              backgroundColor: COLORS.primary.light + '20',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <FaImage style={{ color: COLORS.text.secondary }} />
          </Box>
        );

      case 'nom_marque':
        return (
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.medium,
                color: COLORS.text.dark
              }}
            >
              {row.nom_marque}
            </Typography>
            {row.site_web && (
              <Typography
                variant="caption"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  color: COLORS.text.secondary,
                  fontSize: TYPOGRAPHY.fontSize.xs
                }}
              >
                <a
                  href={row.site_web}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ color: COLORS.primary.main, textDecoration: 'none' }}
                >
                  {row.site_web}
                </a>
              </Typography>
            )}
          </Box>
        );

      case 'description_marque':
        return (
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.dark,
              maxWidth: 200,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {row.description_marque || '-'}
          </Typography>
        );

      case 'actif':
        return (
          <Chip
            label={row.actif ? 'Active' : 'Inactive'}
            color={row.actif ? 'success' : 'error'}
            size="small"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.medium
            }}
          />
        );

      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton variant="outline" size="small" onClick={() => handleEdit(row)} startIcon={<FaEdit />}>
              Éditer
            </StandardButton>
            <StandardButton variant="error" size="small" onClick={() => handleDelete(row.id)} startIcon={<FaTrash />}>
              Supprimer
            </StandardButton>
          </Box>
        );

      default:
        return row[column.id];
    }
  };

  return (
    <MainCard>
      <Box sx={{ width: '100%' }}>
        {/* Breadcrumb - Design System Style */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.sm
            }}
          >
            Accueil &gt; Gestion des Marques
          </Typography>
        </Box>

        {/* Header - Design System Style */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h3"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.text.dark,
              mb: 1
            }}
          >
            Gestion des Marques
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.md
            }}
          >
            Gérez toutes vos marques en un seul endroit
          </Typography>
        </Box>

        {/* Messages d'alerte */}
        {error && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="error" onClose={() => setError('')}>
              {error}
            </Alert>
          </Box>
        )}
        {success && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="success" onClose={() => setSuccess('')}>
              {success}
            </Alert>
          </Box>
        )}

        {/* Bouton pour ajouter une marque */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
          <StandardButton variant="primary" onClick={handleCreate} startIcon={<FaPlus />}>
            Ajouter une marque
          </StandardButton>
        </Box>

        {/* Tableau des marques */}
        <StandardTable
          columns={columns}
          data={brands}
          loading={loading}
          error={error}
          renderCell={renderCell}
          emptyMessage="Aucune marque disponible"
        />

        {/* Modal pour créer/modifier une marque */}
        <ProfessionalModal
          show={showModal}
          onHide={() => setShowModal(false)}
          title={modalAction === 'create' ? 'Ajouter une marque' : 'Modifier la marque'}
          size="lg"
          primaryAction={handleSubmit}
          secondaryAction={() => setShowModal(false)}
          primaryText={modalAction === 'create' ? 'Créer' : 'Modifier'}
          secondaryText="Annuler"
          loading={loading}
          loadingText="Enregistrement..."
        >
          <Form onSubmit={handleSubmit}>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    Nom de la marque <span className="text-danger">*</span>
                  </Form.Label>
                  <Form.Control type="text" name="nom_marque" value={formData.nom_marque} onChange={handleChange} required />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Site web</Form.Label>
                  <Form.Control
                    type="url"
                    name="site_web"
                    value={formData.site_web}
                    onChange={handleChange}
                    placeholder="https://example.com"
                  />
                </Form.Group>
              </Col>

              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    name="description_marque"
                    value={formData.description_marque}
                    onChange={handleChange}
                    rows={3}
                  />
                </Form.Group>
              </Col>

              <Col md={8}>
                <Form.Group className="mb-3">
                  <Form.Label>URL du logo</Form.Label>
                  <Form.Control
                    type="url"
                    name="logo_marque"
                    value={formData.logo_marque}
                    onChange={handleChange}
                    placeholder="https://example.com/logo.png"
                  />
                </Form.Group>
              </Col>

              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Check type="checkbox" name="actif" label="Marque active" checked={formData.actif} onChange={handleChange} />
                </Form.Group>
              </Col>
            </Row>

            {/* Image management for selected brand */}
            {modalAction === 'edit' && currentBrand && (
              <div className="mt-4 pt-3 border-top">
                <h6 className="mb-3 text-muted">Gestion des images</h6>
                <ImageManager modelType="marque" modelId={currentBrand.id} />
              </div>
            )}
          </Form>
        </ProfessionalModal>

        {/* Modal de confirmation de suppression */}
        <ProfessionalModal
          show={showDeleteModal}
          onHide={() => setShowDeleteModal(false)}
          title="Confirmer la suppression"
          size="md"
          primaryAction={confirmDelete}
          secondaryAction={() => setShowDeleteModal(false)}
          primaryText="Supprimer"
          secondaryText="Annuler"
          loading={loading}
          loadingText="Suppression..."
          variant="danger"
        >
          {brandToDelete && (
            <div>
              <p>
                Êtes-vous sûr de vouloir supprimer la marque <strong>"{brandToDelete.nom_marque}"</strong> ?
              </p>

              <div className="alert alert-warning">
                <strong>⚠️ ATTENTION :</strong> Cette action est irréversible.
              </div>

              <div className="alert alert-info">
                <strong>📝 IMPORTANT :</strong> Si cette marque contient des produits, la suppression échouera. Vous devez d'abord supprimer
                tous les produits de cette marque.
              </div>
            </div>
          )}
        </ProfessionalModal>
      </Box>
    </MainCard>
  );
};

export default BrandManagement;
