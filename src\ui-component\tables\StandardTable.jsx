import React from 'react';
import PropTypes from 'prop-types';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Box,
  Typography,
  Alert
} from '@mui/material';
import { COLORS, TYPOGRAPHY } from 'themes/designSystem';

const StandardTable = ({
  columns = [],
  data = [],
  loading = false,
  error = null,
  emptyMessage = 'Aucune donnée disponible',
  renderCell,
  hover = true,
  draggable = false,
  sx = {},
  ...props
}) => {
  const tableStyles = {
    borderRadius: 2,
    overflow: 'hidden',
    border: `1px solid ${COLORS.primary.light}20`,
    ...sx
  };

  const headerCellStyles = {
    fontFamily: TYPOGRAPHY.fontFamily.primary,
    fontWeight: TYPOGRAPHY.fontWeight.semibold,
    color: COLORS.text.dark,
    fontSize: TYPOGRAPHY.fontSize.sm,
    backgroundColor: COLORS.primary.light + '10',
    borderBottom: `2px solid ${COLORS.primary.light}30`,
    py: 2
  };

  const bodyCellStyles = {
    fontFamily: TYPOGRAPHY.fontFamily.primary,
    color: COLORS.text.dark,
    fontSize: TYPOGRAPHY.fontSize.sm,
    borderBottom: `1px solid ${COLORS.primary.light}20`,
    py: 1.5
  };

  const rowStyles = {
    '&:hover': hover ? {
      backgroundColor: COLORS.primary.light + '10',
      transform: 'translateY(-1px)',
      boxShadow: `0 2px 8px ${COLORS.primary.main}10`,
      transition: 'all 0.2s ease-in-out'
    } : {},
    '&:last-child td': {
      borderBottom: 'none'
    },
    ...(draggable && {
      cursor: 'grab',
      '&:active': {
        cursor: 'grabbing'
      }
    })
  };

  if (loading) {
    return (
      <Paper sx={tableStyles}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
          <CircularProgress size={40} sx={{ color: COLORS.primary.main }} />
        </Box>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper sx={tableStyles}>
        <Box sx={{ p: 3 }}>
          <Alert severity="error">{error}</Alert>
        </Box>
      </Paper>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Paper sx={tableStyles}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
          <Typography
            variant="body1"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.md
            }}
          >
            {emptyMessage}
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <TableContainer component={Paper} sx={tableStyles} {...props}>
      <Table>
        <TableHead>
          <TableRow>
            {columns.map((column) => (
              <TableCell
                key={column.id}
                align={column.align || 'left'}
                sx={{
                  ...headerCellStyles,
                  width: column.width,
                  minWidth: column.minWidth
                }}
              >
                {column.label}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, index) => (
            <TableRow key={row.id || index} sx={rowStyles}>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || 'left'}
                  sx={bodyCellStyles}
                >
                  {renderCell ? renderCell(column, row, index) : row[column.id]}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

StandardTable.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      align: PropTypes.oneOf(['left', 'center', 'right']),
      width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      minWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
    })
  ).isRequired,
  data: PropTypes.array,
  loading: PropTypes.bool,
  error: PropTypes.string,
  emptyMessage: PropTypes.string,
  renderCell: PropTypes.func,
  hover: PropTypes.bool,
  draggable: PropTypes.bool,
  sx: PropTypes.object
};

export default StandardTable;
