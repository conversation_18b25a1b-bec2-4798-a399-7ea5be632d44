import { useEffect } from 'react';
import { useGetMenuMaster } from 'api/menu';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';

/**
 * Modal Position Manager Component
 * Automatically manages modal positioning based on sidebar state
 * Should be placed at the app level to work globally
 */
const ModalPositionManager = () => {
  const theme = useTheme();
  const downMD = useMediaQuery(theme.breakpoints.down('md'));
  const { menuMaster } = useGetMenuMaster();
  const drawerOpen = menuMaster?.isDashboardDrawerOpened;

  useEffect(() => {
    const updateModalPositioning = () => {
      const root = document.documentElement;
      
      // On mobile/tablet, sidebar is typically hidden
      if (downMD) {
        root.classList.add('sidebar-closed');
        root.classList.remove('sidebar-open');
        root.style.setProperty('--current-sidebar-width', '0px');
      } else {
        // Desktop behavior
        if (drawerOpen) {
          // Sidebar is open (260px width)
          root.classList.add('sidebar-open');
          root.classList.remove('sidebar-closed');
          root.style.setProperty('--current-sidebar-width', '260px');
        } else {
          // Sidebar is closed/mini (72px width)
          root.classList.add('sidebar-closed');
          root.classList.remove('sidebar-open');
          root.style.setProperty('--current-sidebar-width', '72px');
        }
      }
    };

    updateModalPositioning();
  }, [drawerOpen, downMD]);

  // This component doesn't render anything
  return null;
};

export default ModalPositionManager;
