import React, { useState, useEffect } from 'react';
import { Box, Typography, TextField, InputAdornment, Chip, IconButton, Tooltip } from '@mui/material';
import {
  FaFileInvoice,
  FaEye,
  FaPrint,
  FaDownload,
  FaHome,
  FaSearch,
  FaCalendarAlt,
  FaMoneyBillWave,
  FaUser,
  FaCreditCard,
  FaServer,
  FaTimes,
  FaShoppingCart
} from 'react-icons/fa';
// Bootstrap components removed - using only ProfessionalModal

// Professional Modal Component
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';

// Standardized components
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';
import StandardButton from '../../components/StandardButton';
import MainCard from '../../ui-component/cards/MainCard';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';

const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

/**
 * Invoices Component
 *
 * This component fetches invoice data using the /api/paiements endpoint to get the list of invoices,
 * but for the payment method (Méthode de paiement) field specifically, it uses ONLY the
 * 'methode_paiement' field directly from the /api/commandes response (not from paiement relationship).
 */
const Invoices = () => {
  // States
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Modal states
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [modalLoading, setModalLoading] = useState(false);

  // Load invoices on component mount
  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch payments which represent our invoices
      const paymentsResponse = await fetch(`${API_URL}/paiements`);
      if (!paymentsResponse.ok) {
        throw new Error('Erreur lors du chargement des paiements');
      }

      const paymentsData = await paymentsResponse.json();
      const payments = Array.isArray(paymentsData) ? paymentsData : paymentsData.data || [];

      console.log('💳 Payments API response:', paymentsData);
      console.log('💳 Processed payments:', payments);

      // For each payment, fetch the related order to get complete invoice data including payment method from commande API
      const invoicesWithOrders = await Promise.all(
        payments.map(async (payment) => {
          try {
            // Fetch order data to get methode_paiement field directly from commande API
            console.log(
              `🔗 Fetching order ${payment.commande_id} from: ${API_URL}/commandes/${payment.commande_id}?with=user,client,produits`
            );
            const orderResponse = await fetch(`${API_URL}/commandes/${payment.commande_id}?with=user,client,produits`);

            console.log(`📡 Order response status for ${payment.commande_id}:`, orderResponse.status, orderResponse.ok);

            if (orderResponse.ok) {
              const orderResponseData = await orderResponse.json();
              console.log(`📦 Raw order response for ${payment.commande_id}:`, orderResponseData);

              // Extract the actual order data from the response
              const orderData = orderResponseData.data || orderResponseData;
              console.log(`📋 Extracted order data for ${payment.commande_id}:`, orderData);

              // Debug logging to see what payment method we're getting
              console.log('🔍 Payment method debug for order', payment.commande_id, ':', {
                'orderData.methode_paiement': orderData.methode_paiement
              });

              // Use ONLY methode_paiement field directly from API commande
              const finalPaymentMethod = orderData.methode_paiement || null;

              console.log('🎯 Final payment method selected:', finalPaymentMethod);

              const invoiceObject = {
                ...payment,
                order: orderData,
                invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
                invoice_date: payment.created_at,
                amount: payment.montant,
                status: payment.status || 'completed',
                // Use payment method from commande API (put AFTER spread to override)
                methode_paiement: finalPaymentMethod
              };

              console.log(`✅ Final invoice object for ${payment.commande_id}:`, {
                'invoice.methode_paiement': invoiceObject.methode_paiement,
                'payment spread keys': Object.keys(payment),
                'payment.methode_paiement': payment.methode_paiement,
                'full invoice': invoiceObject
              });

              return invoiceObject;
            }
            console.log('⚠️ Order fetch failed, keeping payment method as null for payment:', payment.id);

            return {
              ...payment,
              order: null,
              // Don't use payment data fallback - keep null if order fetch fails
              methode_paiement: null,
              invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
              invoice_date: payment.created_at,
              amount: payment.montant,
              status: payment.status || 'completed'
            };
          } catch (err) {
            console.error(`❌ Error fetching order ${payment.commande_id}:`, err);

            return {
              ...payment,
              order: null,
              // Don't use payment data fallback - keep null if error occurs
              methode_paiement: null,
              invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
              invoice_date: payment.created_at,
              amount: payment.montant,
              status: payment.status || 'completed'
            };
          }
        })
      );

      setInvoices(invoicesWithOrders);
    } catch (err) {
      console.error('Error loading invoices:', err);
      setError('Erreur lors du chargement des factures: ' + err.message);
      setInvoices([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter invoices based on search term
  const filteredInvoices = invoices.filter(
    (invoice) =>
      invoice.invoice_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.nom_client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.prenom_client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.email_commande?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.transaction_id?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic
  const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentInvoices = filteredInvoices.slice(startIndex, endIndex);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Table columns configuration
  const columns = [
    { id: 'invoice_number', label: 'N° Facture', minWidth: 120 },
    { id: 'invoice_date', label: 'Date', minWidth: 150 },
    { id: 'client_name', label: 'Client', minWidth: 180 },
    { id: 'commande_id', label: 'Commande', minWidth: 100 },
    { id: 'amount', label: 'Montant', minWidth: 120 },
    { id: 'status', label: 'Statut', minWidth: 100 },
    { id: 'actions', label: 'Actions', minWidth: 120 }
  ];

  // Custom cell renderer
  const renderCell = (column, row, value) => {
    switch (column.id) {
      case 'invoice_number':
        return (
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.semibold,
                color: COLORS.primary.main
              }}
            >
              {row.invoice_number}
            </Typography>
            {row.transaction_id && (
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                ID: {row.transaction_id}
              </Typography>
            )}
          </Box>
        );
      case 'invoice_date':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
            {formatDate(row.invoice_date)}
          </Typography>
        );
      case 'client_name':
        return (
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.medium
              }}
            >
              {getClientName(row)}
            </Typography>
            {(row.order?.email_commande || row.order?.user?.email || row.order?.client?.email) && (
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                {row.order.email_commande || row.order.user?.email || row.order.client?.email}
              </Typography>
            )}
          </Box>
        );
      case 'commande_id':
        return (
          <Chip
            label={`CMD-${row.commande_id}`}
            size="small"
            sx={{
              backgroundColor: COLORS.primary.light,
              color: COLORS.primary.main,
              fontFamily: TYPOGRAPHY.fontFamily.primary
            }}
          />
        );
      case 'amount':
        return (
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.success.main
            }}
          >
            {formatCurrency(row.amount)}
          </Typography>
        );
      case 'status':
        const statusColors = {
          completed: COLORS.success.main,
          pending: COLORS.warning.main,
          failed: COLORS.error.main
        };
        return (
          <Chip
            label={getStatusText(row.status)}
            size="small"
            sx={{
              backgroundColor: statusColors[row.status?.toLowerCase()] || COLORS.grey[500],
              color: 'white',
              fontFamily: TYPOGRAPHY.fontFamily.primary
            }}
          />
        );
      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Voir la facture">
              <IconButton size="small" onClick={() => handleViewInvoice(row)} sx={{ color: COLORS.primary.main }}>
                <FaEye />
              </IconButton>
            </Tooltip>
            <Tooltip title="Imprimer">
              <IconButton size="small" onClick={() => handlePrintInvoice(row)} sx={{ color: COLORS.secondary.main }}>
                <FaPrint />
              </IconButton>
            </Tooltip>
          </Box>
        );
      default:
        return value || 'N/A';
    }
  };

  // Handle view invoice details
  const handleViewInvoice = async (invoice) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  // Handle print invoice
  const handlePrintInvoice = (invoice) => {
    console.log('📄 handlePrintInvoice called with:', invoice);

    // Créer le contenu HTML directement
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Facture ${invoice.invoice_number || `FAC-${invoice.id}`} - JihenLine</title>
        <meta charset="utf-8">
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 20px;
          }

          .print-container {
            max-width: 800px;
            margin: 0 auto;
          }

          .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 3px solid #1976d2;
            padding-bottom: 20px;
          }

          .company-info h1 {
            font-size: 28px;
            color: #1976d2;
            margin-bottom: 5px;
            font-weight: bold;
          }

          .company-info p {
            margin: 2px 0;
            color: #666;
          }

          .document-info {
            text-align: right;
          }

          .document-info h2 {
            font-size: 24px;
            color: #1976d2;
            margin-bottom: 10px;
          }

          .section {
            margin: 25px 0;
          }

          .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 5px;
          }

          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
          }

          .info-item {
            margin-bottom: 8px;
          }

          .info-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            min-width: 120px;
          }

          .total-section {
            margin-top: 20px;
            text-align: right;
          }

          .total-row {
            display: flex;
            justify-content: flex-end;
            margin: 5px 0;
          }

          .total-label {
            min-width: 150px;
            font-weight: bold;
            padding: 5px 10px;
          }

          .total-value {
            min-width: 100px;
            padding: 5px 10px;
            text-align: right;
          }

          .grand-total {
            background-color: #1976d2;
            color: white;
            font-size: 16px;
            font-weight: bold;
          }

          .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
            color: #666;
            font-size: 11px;
          }

          @media print {
            body {
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
              padding: 0;
            }

            .print-container {
              max-width: none;
              margin: 0;
            }
          }
        </style>
      </head>
      <body>
        <div class="print-container">
          <!-- Header -->
          <div class="header">
            <div class="company-info">
              <h1>JihenLine</h1>
              <p>Plateforme E-commerce</p>
              <p>Tunis, Tunisie</p>
              <p>Email: <EMAIL></p>
              <p>Tél: +216 XX XXX XXX</p>
              <p>TVA: TN123456789</p>
              <p>RC: B123456789</p>
            </div>
            <div class="document-info">
              <h2>FACTURE</h2>
              <p><strong>N°:</strong> ${invoice.invoice_number || `FAC-${invoice.id}`}</p>
              <p><strong>Date:</strong> ${formatDate(invoice.invoice_date || invoice.created_at)}</p>
              <p><strong>Statut:</strong> Payée</p>
            </div>
          </div>

          <!-- Invoice Info -->
          <div class="section">
            <div class="section-title">Informations Facture</div>
            <div class="info-grid">
              <div>
                <div class="info-item">
                  <span class="info-label">Commande N°:</span>
                  <span class="info-value">CMD-${invoice.commande_id}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Client:</span>
                  <span class="info-value">${getClientName(invoice)}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Email:</span>
                  <span class="info-value">${invoice.order?.user?.email || invoice.order?.email_commande || 'N/A'}</span>
                </div>
              </div>
              <div>
                <div class="info-item">
                  <span class="info-label">Méthode de paiement:</span>
                  <span class="info-value">${invoice.methode_paiement || 'N/A'}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Date de paiement:</span>
                  <span class="info-value">${formatDate(invoice.created_at)}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Payment Details -->
          <div class="section">
            <div class="section-title">Détails du Paiement</div>
            <div class="total-section">
              <div class="total-row">
                <div class="total-label">Montant HT:</div>
                <div class="total-value">${formatCurrency((invoice.amount || invoice.montant || 0) / 1.19)}</div>
              </div>
              <div class="total-row">
                <div class="total-label">TVA (19%):</div>
                <div class="total-value">${formatCurrency(((invoice.amount || invoice.montant || 0) * 0.19) / 1.19)}</div>
              </div>
              <div class="total-row grand-total">
                <div class="total-label">TOTAL TTC</div>
                <div class="total-value">${formatCurrency(invoice.amount || invoice.montant || 0)}</div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>Merci pour votre confiance - JihenLine</p>
            <p>Facture générée le ${formatDate(new Date().toISOString())}</p>
            <p>TVA: TN123456789 - RC: B123456789</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // Ouvrir nouvelle fenêtre et imprimer
    const printWindow = window.open('', '_blank');
    console.log('🖨️ Print window opened:', !!printWindow);

    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      console.log('🖨️ Content written to print window, starting print...');

      setTimeout(() => {
        printWindow.print();
        printWindow.close();
        console.log('🖨️ Print completed and window closed');
      }, 500);
    } else {
      console.error('❌ Failed to open print window - popup blocked?');
      alert("Impossible d'ouvrir la fenêtre d'impression. Veuillez autoriser les popups pour ce site.");
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format currency - Using TND (Tunisian Dinar)
  const formatCurrency = (amount) => {
    if (!amount) return '0,000 TND';
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  // Get client name with improved fallback logic
  const getClientName = (invoice) => {
    const order = invoice.order;
    if (!order) return 'Client inconnu';

    // Try different sources for client name
    if (order.user?.name) return order.user.name;
    if (order.client?.nom) return order.client.nom;
    if (order.client?.name) return order.client.name;
    if (order.nom_client) return order.nom_client;
    if (order.prenom_client) return order.prenom_client;

    // Try to construct name from first and last name
    if (order.prenom_client && order.nom_client) {
      return `${order.prenom_client} ${order.nom_client}`;
    }

    // Fallback to email username if available
    if (order.email_commande) {
      return order.email_commande.split('@')[0];
    }
    if (order.user?.email) {
      return order.user.email.split('@')[0];
    }
    if (order.client?.email) {
      return order.client.email.split('@')[0];
    }

    return 'Client inconnu';
  };

  // Get status badge variant
  const getStatusBadge = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  // Get status text
  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return 'Payée';
      case 'pending':
        return 'En attente';
      case 'failed':
        return 'Échec';
      default:
        return 'Inconnue';
    }
  };

  return (
    <MainCard title="Gestion des Factures - Enhanced">
      <Box sx={{ width: '100%' }}>
        {/* Search Field */}
        <StandardCard title="Recherche" size="small" sx={{ mb: 3 }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Rechercher une facture (numéro, client, email, transaction)..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{
              '& .MuiInputBase-root': {
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontSize: TYPOGRAPHY.fontSize.sm
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <FaSearch size={20} color={COLORS.grey[500]} />
                </InputAdornment>
              )
            }}
          />
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" sx={{ color: COLORS.text.secondary }}>
              {filteredInvoices.length > 0 && (
                <>
                  Affichage {startIndex + 1}-{Math.min(endIndex, filteredInvoices.length)} sur {filteredInvoices.length} factures
                </>
              )}
            </Typography>
            <StandardButton variant="outline" onClick={loadInvoices} loading={loading} disabled={loading}>
              {loading ? 'Actualisation...' : 'Actualiser'}
            </StandardButton>
          </Box>
        </StandardCard>

        {/* Results Summary */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="h5"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontSize: TYPOGRAPHY.fontSize.lg,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.text.dark
            }}
          >
            Toutes les Factures ({filteredInvoices.length})
          </Typography>
          {searchTerm && (
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                mt: 0.5
              }}
            >
              Résultats pour "{searchTerm}"
            </Typography>
          )}
        </Box>

        {/* Enhanced Table */}
        <StandardTable
          columns={columns}
          data={currentInvoices}
          loading={loading}
          error={error}
          emptyMessage={searchTerm ? 'Aucune facture ne correspond à votre recherche.' : 'Aucune facture disponible pour le moment.'}
          renderCell={renderCell}
          hover={true}
          pagination={{
            page: currentPage,
            totalPages: totalPages,
            onPageChange: handlePageChange
          }}
        />

        {/* Additional Info */}
        {!loading && !error && filteredInvoices.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                textAlign: 'center'
              }}
            >
              Page {currentPage} sur {totalPages}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Invoice Details Modal */}
      <ProfessionalModal
        open={showInvoiceModal}
        onClose={() => setShowInvoiceModal(false)}
        title={`Facture ${selectedInvoice?.invoice_number || `FAC-${selectedInvoice?.id}`}`}
        icon={<FaFileInvoice />}
        maxWidth="lg"
        fullWidth
      >
        {selectedInvoice && (
          <Box sx={{ p: 3 }}>
            {/* Company Header */}
            <Box sx={{ mb: 4, pb: 3, borderBottom: 3, borderColor: COLORS.primary.main }}>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>
                <Box>
                  <Typography
                    variant="h5"
                    sx={{
                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                      fontWeight: TYPOGRAPHY.fontWeight.bold,
                      color: COLORS.primary.main,
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <FaHome style={{ marginRight: 8 }} />
                    JihenLine
                  </Typography>
                  <Typography variant="body2" sx={{ color: COLORS.text.secondary, mb: 0.5 }}>
                    Plateforme E-commerce
                  </Typography>
                  <Typography variant="body2" sx={{ color: COLORS.text.secondary, mb: 0.5 }}>
                    Email: <EMAIL>
                  </Typography>
                  <Typography variant="body2" sx={{ color: COLORS.text.secondary, mb: 0.5 }}>
                    Téléphone: +216 XX XXX XXX
                  </Typography>
                  <Typography variant="body2" sx={{ color: COLORS.text.secondary, mb: 0.5 }}>
                    TVA: TN123456789
                  </Typography>
                  <Typography variant="body2" sx={{ color: COLORS.text.secondary }}>
                    RC: B123456789
                  </Typography>
                </Box>
                <Box sx={{ textAlign: { xs: 'left', md: 'right' } }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                      fontWeight: TYPOGRAPHY.fontWeight.bold,
                      color: COLORS.primary.main,
                      mb: 2
                    }}
                  >
                    FACTURE
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>N°:</strong> {selectedInvoice.invoice_number || `FAC-${selectedInvoice.id}`}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Date:</strong> {formatDate(selectedInvoice.invoice_date || selectedInvoice.created_at)}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Statut:</strong>{' '}
                    <Chip label="Payée" size="small" sx={{ backgroundColor: COLORS.success.main, color: 'white', ml: 1 }} />
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Client Information */}
            <Box sx={{ mb: 4 }}>
              <Typography
                variant="h6"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  fontWeight: TYPOGRAPHY.fontWeight.semibold,
                  color: COLORS.primary.main,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <FaUser style={{ marginRight: 8 }} />
                Informations Client
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', color: COLORS.text.secondary, mb: 0.5 }}>
                    Client:
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    {getClientName(selectedInvoice)}
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', color: COLORS.text.secondary, mb: 0.5 }}>
                    Email:
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    {selectedInvoice.order?.user?.email ||
                      selectedInvoice.order?.email_commande ||
                      selectedInvoice.order?.client?.email ||
                      'Non spécifié'}
                  </Typography>
                  {selectedInvoice.order?.telephone_commande && (
                    <>
                      <Typography variant="body2" sx={{ fontWeight: 'bold', color: COLORS.text.secondary, mb: 0.5 }}>
                        Téléphone:
                      </Typography>
                      <Typography variant="body2">{selectedInvoice.order.telephone_commande}</Typography>
                    </>
                  )}
                </Box>
                {selectedInvoice.order?.adresse_commande && (
                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', color: COLORS.text.secondary, mb: 0.5 }}>
                      Adresse de livraison:
                    </Typography>
                    <Typography variant="body2">
                      {selectedInvoice.order.adresse_commande}
                      {selectedInvoice.order?.ville_commande && (
                        <>
                          <br />
                          {selectedInvoice.order.ville_commande}
                        </>
                      )}
                      {selectedInvoice.order?.code_postal_commande && <> {selectedInvoice.order.code_postal_commande}</>}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>

            {/* Payment Information */}
            <Box sx={{ mb: 4 }}>
              <Typography
                variant="h6"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  fontWeight: TYPOGRAPHY.fontWeight.semibold,
                  color: COLORS.primary.main,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <FaCreditCard style={{ marginRight: 8 }} />
                Informations de Paiement
              </Typography>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', color: COLORS.text.secondary, mb: 0.5 }}>
                      Méthode de paiement:
                    </Typography>
                    <Chip
                      label={selectedInvoice.methode_paiement ? selectedInvoice.methode_paiement.replace('_', ' ') : 'Non spécifiée'}
                      size="small"
                      sx={{ mb: 2 }}
                    />
                    {selectedInvoice.transaction_id && (
                      <>
                        <Typography variant="body2" sx={{ fontWeight: 'bold', color: COLORS.text.secondary, mb: 0.5 }}>
                          Transaction ID:
                        </Typography>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace', mb: 2 }}>
                          {selectedInvoice.transaction_id}
                        </Typography>
                      </>
                    )}
                    <Typography variant="body2" sx={{ fontWeight: 'bold', color: COLORS.text.secondary, mb: 0.5 }}>
                      Date de paiement:
                    </Typography>
                    <Typography variant="body2">{formatDate(selectedInvoice.created_at)}</Typography>
                  </Box>
                  <Box sx={{ textAlign: 'right' }}>
                    <Typography variant="body2" sx={{ color: COLORS.text.secondary, mb: 0.5 }}>
                      Montant Payé
                    </Typography>
                    <Typography
                      variant="h5"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.bold,
                        color: 'success.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'flex-end'
                      }}
                    >
                      <FaMoneyBillWave style={{ marginRight: 4 }} />
                      {formatCurrency(selectedInvoice.amount)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>

            {/* Total Section */}
            <Box
              sx={{
                mt: 3,
                p: 2,
                bgcolor: COLORS.primary.light + '10',
                borderRadius: 1,
                borderLeft: 4,
                borderColor: COLORS.primary.main
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontWeight: TYPOGRAPHY.fontWeight.bold,
                    color: COLORS.primary.main
                  }}
                >
                  Montant Total TTC:
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontWeight: TYPOGRAPHY.fontWeight.bold,
                    color: COLORS.primary.main
                  }}
                >
                  {formatCurrency(selectedInvoice.amount)}
                </Typography>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  color: COLORS.text.secondary,
                  mt: 1
                }}
              >
                TVA incluse (19%) - Facture générée le {formatDate(new Date().toISOString())}
              </Typography>
            </Box>
          </Box>
        )}
      </ProfessionalModal>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          /* Hide non-essential elements */
          .btn,
          .breadcrumb,
          .alert,
          .modal-header,
          .modal-footer,
          .navbar,
          .sidebar,
          .container-fluid > *:not(.modal),
          body > *:not(.modal) {
            display: none !important;
          }

          /* Show only the modal content */
          .modal,
          .modal-dialog,
          .modal-content,
          .modal-body {
            display: block !important;
            position: static !important;
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 0 !important;
            max-height: none !important;
            overflow: visible !important;
            background: white !important;
            border: none !important;
            box-shadow: none !important;
          }

          /* Invoice styling */
          .invoice-details {
            margin: 0 !important;
            padding: 20px !important;
            background: white !important;
            font-size: 12px !important;
            line-height: 1.4 !important;
          }

          /* Headers and sections */
          .invoice-header {
            border-bottom: 2px solid #0d6efd !important;
            margin-bottom: 20px !important;
            padding-bottom: 15px !important;
          }

          .invoice-header h5 {
            font-size: 18px !important;
            margin-bottom: 10px !important;
          }

          h6 {
            font-size: 14px !important;
            margin-bottom: 10px !important;
            color: #0d6efd !important;
            border-bottom: 1px solid #dee2e6 !important;
            padding-bottom: 5px !important;
          }

          /* Background and borders */
          .bg-light {
            background: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
            padding: 10px !important;
          }

          /* Tables */
          .table {
            font-size: 11px !important;
            margin-bottom: 15px !important;
          }

          .table th {
            background: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
            padding: 8px !important;
            font-weight: bold !important;
          }

          .table td {
            border: 1px solid #dee2e6 !important;
            padding: 6px !important;
          }

          /* Colors and text */
          .text-primary {
            color: #0d6efd !important;
          }

          .text-success {
            color: #198754 !important;
          }

          .text-muted {
            color: #6c757d !important;
          }

          /* Badges */
          .badge {
            border: 1px solid #000 !important;
            padding: 2px 6px !important;
            font-size: 10px !important;
          }

          /* Totals section */
          .order-totals {
            margin-top: 20px !important;
            border-top: 2px solid #0d6efd !important;
            padding-top: 15px !important;
          }

          /* Company info */
          .invoice-header .text-primary {
            font-weight: bold !important;
            font-size: 20px !important;
          }

          /* Page breaks */
          .payment-info,
          .gateway-info {
            page-break-inside: avoid !important;
          }

          /* Hide gateway response for cleaner print */
          .gateway-info {
            display: none !important;
          }
        }
      `}</style>
    </MainCard>
  );
};

export default Invoices;
