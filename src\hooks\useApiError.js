import { useState, useCallback } from 'react';

export const useApiError = () => {
  const [error, setError] = useState(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const handleError = useCallback((error, context = {}) => {
    console.error('🚨 API Error handled:', {
      error: error.message,
      status: error.response?.status,
      context,
      timestamp: new Date().toISOString()
    });

    // Analyser le type d'erreur
    const errorType = getErrorType(error);
    const errorMessage = getErrorMessage(error, context);

    setError({
      type: errorType,
      message: errorMessage,
      originalError: error,
      context,
      timestamp: new Date().toISOString(),
      canRetry: canRetryError(error)
    });
  }, []);

  const retry = useCallback(async (retryFunction) => {
    if (!retryFunction) return;

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    try {
      await retryFunction();
      clearError();
    } catch (error) {
      handleError(error, { isRetry: true, retryAttempt: retryCount + 1 });
    } finally {
      setIsRetrying(false);
    }
  }, [retryCount, handleError]);

  const clearError = useCallback(() => {
    setError(null);
    setRetryCount(0);
  }, []);

  return {
    error,
    isRetrying,
    retryCount,
    handleError,
    retry,
    clearError
  };
};

// Fonctions utilitaires
const getErrorType = (error) => {
  if (!error.response) return 'NETWORK_ERROR';
  
  const status = error.response.status;
  if (status >= 500) return 'SERVER_ERROR';
  if (status === 404) return 'NOT_FOUND';
  if (status === 401) return 'UNAUTHORIZED';
  if (status === 403) return 'FORBIDDEN';
  if (status >= 400) return 'CLIENT_ERROR';
  
  return 'UNKNOWN_ERROR';
};

const getErrorMessage = (error, context) => {
  const status = error.response?.status;
  const serverMessage = error.response?.data?.message || error.response?.data?.error;

  switch (status) {
    case 500:
      return `Erreur serveur (500): ${serverMessage || 'Le serveur rencontre un problème interne'}. ${
        context.resource ? `Impossible de charger ${context.resource}.` : ''
      } Veuillez réessayer dans quelques instants.`;
    
    case 502:
      return 'Service temporairement indisponible (502). Le serveur est en maintenance.';
    
    case 503:
      return 'Service indisponible (503). Maintenance en cours, veuillez patienter.';
    
    case 504:
      return 'Délai d\'attente dépassé (504). Le serveur met trop de temps à répondre.';
    
    case 404:
      return `${context.resource || 'Ressource'} introuvable (404).`;
    
    case 401:
      return 'Session expirée (401). Veuillez vous reconnecter.';
    
    case 403:
      return 'Accès refusé (403). Vous n\'avez pas les permissions nécessaires.';
    
    default:
      if (!error.response) {
        return 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';
      }
      return serverMessage || error.message || 'Une erreur inattendue s\'est produite.';
  }
};

const canRetryError = (error) => {
  const status = error.response?.status;
  
  // Ne pas réessayer pour les erreurs client (4xx sauf 408, 429)
  if (status >= 400 && status < 500) {
    return status === 408 || status === 429; // Timeout ou Rate limit
  }
  
  // Réessayer pour les erreurs serveur (5xx) et réseau
  return !error.response || status >= 500;
};

// Hook spécialisé pour les commandes
export const useOrderError = () => {
  const apiError = useApiError();

  const handleOrderError = useCallback((error, orderId = null) => {
    const context = {
      resource: orderId ? `la commande #${orderId}` : 'les commandes',
      orderId,
      action: orderId ? 'fetch_single' : 'fetch_list'
    };

    apiError.handleError(error, context);
  }, [apiError]);

  return {
    ...apiError,
    handleOrderError
  };
};

export default useApiError;
