// material-ui
import { styled } from '@mui/material/styles';

// project imports
import { drawerWidth } from 'store/constant';

// ==============================|| MAIN LAYOUT - STYLED ||============================== //

const MainContentStyled = styled('main', {
  shouldForwardProp: (prop) => prop !== 'open' && prop !== 'borderRadius'
})(({ theme, open, borderRadius }) => ({
  backgroundColor: theme.palette.grey[100],
  minWidth: 0, // Changed from '1%' to 0 for better flex behavior
  minHeight: 'calc(100vh - 88px)',
  flexGrow: 1,
  padding: 20,
  marginTop: 88,
  marginRight: 20,
  borderRadius: `${borderRadius}px`,
  borderBottomLeftRadius: 0,
  borderBottomRightRadius: 0,
  transition: theme.transitions.create(['margin', 'width', 'margin-left'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen + 200 // Increased duration for smoother transition
  }),
  // Desktop behavior
  [theme.breakpoints.up('md')]: {
    marginLeft: open ? `${drawerWidth}px` : '72px', // Use marginLeft instead of width calculation
    width: 'auto', // Let it fill remaining space automatically
    marginTop: 88
  },
  // Mobile behavior
  [theme.breakpoints.down('md')]: {
    marginLeft: 20,
    padding: 16,
    marginTop: 88,
    width: open ? `calc(100% - ${drawerWidth}px)` : 'calc(100% - 40px)'
  },
  [theme.breakpoints.down('sm')]: {
    marginLeft: 10,
    marginRight: 10,
    width: 'calc(100% - 20px)'
  }
}));

export default MainContentStyled;
