import React from 'react';
import { Pagination, Form } from 'react-bootstrap';

/**
 * Reusable Table Pagination Component
 * Based on the invoice page pagination style
 * 
 * @param {Object} props
 * @param {number} props.currentPage - Current active page
 * @param {number} props.totalPages - Total number of pages
 * @param {number} props.totalItems - Total number of items
 * @param {number} props.itemsPerPage - Items per page
 * @param {number} props.startIndex - Start index of current page items
 * @param {number} props.endIndex - End index of current page items
 * @param {Function} props.onPageChange - Callback when page changes
 * @param {Function} props.onItemsPerPageChange - Callback when items per page changes (optional)
 * @param {Array} props.itemsPerPageOptions - Options for items per page (optional)
 * @param {boolean} props.showItemsPerPage - Whether to show items per page selector (default: true)
 * @param {boolean} props.showPageInfo - Whether to show page info text (default: true)
 * @param {boolean} props.showDirectPageInput - Whether to show direct page input (default: false)
 * @param {string} props.size - Pagination size: 'sm', 'lg', or default
 */
const TablePagination = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  startIndex,
  endIndex,
  onPageChange,
  onItemsPerPageChange,
  itemsPerPageOptions = [10, 15, 25, 50, 100],
  showItemsPerPage = true,
  showPageInfo = true,
  showDirectPageInput = false,
  size = 'default'
}) => {
  const [pageInputValue, setPageInputValue] = React.useState(currentPage.toString());

  // Update page input when current page changes
  React.useEffect(() => {
    setPageInputValue(currentPage.toString());
  }, [currentPage]);

  const handleDirectPageInput = (value) => {
    setPageInputValue(value);
    const page = parseInt(value);
    if (!isNaN(page) && page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  const handlePageInputBlur = (e) => {
    const page = parseInt(e.target.value);
    if (isNaN(page) || page < 1 || page > totalPages) {
      setPageInputValue(currentPage.toString());
    }
  };

  const handlePageInputKeyPress = (e) => {
    if (e.key === 'Enter') {
      const page = parseInt(e.target.value);
      if (page >= 1 && page <= totalPages) {
        onPageChange(page);
      }
    }
  };

  // Don't render if there's only one page and no items per page selector
  if (totalPages <= 1 && !showItemsPerPage) {
    return null;
  }

  return (
    <div className="d-flex justify-content-between align-items-center mt-3">
      {/* Left side - Page info and items per page */}
      <div className="d-flex align-items-center">
        {showPageInfo && (
          <span className="text-muted small me-3">
            {totalItems > 0 
              ? `Affichage de ${startIndex + 1} à ${Math.min(endIndex, totalItems)} sur ${totalItems} élément(s)`
              : 'Aucun élément'
            }
          </span>
        )}
        
        {showItemsPerPage && onItemsPerPageChange && (
          <Form.Select
            size="sm"
            style={{ width: 'auto' }}
            value={itemsPerPage}
            onChange={(e) => onItemsPerPageChange(parseInt(e.target.value))}
          >
            {itemsPerPageOptions.map(option => (
              <option key={option} value={option}>
                {option} par page
              </option>
            ))}
          </Form.Select>
        )}
      </div>

      {/* Right side - Pagination controls */}
      {totalPages > 1 && (
        <div className="d-flex align-items-center">
          {/* Page info for small screens */}
          <div className="text-muted small me-3 d-md-none">
            Page {currentPage} sur {totalPages}
          </div>

          {/* Pagination */}
          <Pagination className={`mb-0 ${size === 'sm' ? 'pagination-sm' : size === 'lg' ? 'pagination-lg' : ''}`}>
            <Pagination.First 
              onClick={() => onPageChange(1)} 
              disabled={currentPage === 1} 
            />
            <Pagination.Prev 
              onClick={() => onPageChange(currentPage - 1)} 
              disabled={currentPage === 1} 
            />

            {/* Smart page number display */}
            {(() => {
              const pages = [];
              const showEllipsis = totalPages > 7;

              if (!showEllipsis) {
                // Show all pages if 7 or fewer
                for (let i = 1; i <= totalPages; i++) {
                  pages.push(i);
                }
              } else {
                // Smart pagination for many pages
                if (currentPage <= 4) {
                  // Show first 5 pages + ellipsis + last page
                  for (let i = 1; i <= 5; i++) pages.push(i);
                  if (totalPages > 6) pages.push('...');
                  pages.push(totalPages);
                } else if (currentPage >= totalPages - 3) {
                  // Show first page + ellipsis + last 5 pages
                  pages.push(1);
                  if (totalPages > 6) pages.push('...');
                  for (let i = totalPages - 4; i <= totalPages; i++) pages.push(i);
                } else {
                  // Show first + ellipsis + current-1, current, current+1 + ellipsis + last
                  pages.push(1);
                  pages.push('...');
                  for (let i = currentPage - 1; i <= currentPage + 1; i++) pages.push(i);
                  pages.push('...');
                  pages.push(totalPages);
                }
              }

              return pages.map((page, index) => {
                if (page === '...') {
                  return (
                    <Pagination.Ellipsis key={`ellipsis-${index}`} disabled />
                  );
                }

                return (
                  <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => onPageChange(page)}
                  >
                    {page}
                  </Pagination.Item>
                );
              });
            })()}

            <Pagination.Next 
              onClick={() => onPageChange(currentPage + 1)} 
              disabled={currentPage === totalPages} 
            />
            <Pagination.Last 
              onClick={() => onPageChange(totalPages)} 
              disabled={currentPage === totalPages} 
            />
          </Pagination>

          {/* Direct page input */}
          {showDirectPageInput && totalPages > 5 && (
            <div className="d-flex align-items-center ms-3">
              <span className="text-muted small me-2">Aller à:</span>
              <Form.Control
                type="number"
                size="sm"
                style={{ width: '70px' }}
                min="1"
                max={totalPages}
                value={pageInputValue}
                onChange={(e) => handleDirectPageInput(e.target.value)}
                onKeyPress={handlePageInputKeyPress}
                onBlur={handlePageInputBlur}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TablePagination;
