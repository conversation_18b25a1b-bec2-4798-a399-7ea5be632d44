// material-ui
import { useTheme } from '@mui/material/styles';
import { Typography, Box } from '@mui/material';

// Design system
import { COLORS, TYPOGRAPHY } from 'themes/designSystem';

// ==============================|| LOGO ||============================== //

export default function Logo() {
  const theme = useTheme();

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
      {/* Logo Icon */}
      <Box
        sx={{
          width: 36,
          height: 36,
          borderRadius: 2,
          background: `linear-gradient(135deg, ${COLORS.primary.main} 0%, ${COLORS.primary.dark} 100%)`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: `0 4px 12px ${COLORS.primary.main}30`
        }}
      >
        <Typography
          variant="h5"
          sx={{
            fontFamily: TYPOGRAPHY.fontFamily.primary,
            fontWeight: TYPOGRAPHY.fontWeight.bold,
            color: 'white',
            fontSize: '1.2rem'
          }}
        >
          JL
        </Typography>
      </Box>

      {/* Text Logo */}
      <Typography
        variant="h4"
        sx={{
          fontFamily: TYPOGRAPHY.fontFamily.primary,
          fontWeight: TYPOGRAPHY.fontWeight.bold,
          color: COLORS.text.dark,
          textDecoration: 'none',
          fontSize: '1.4rem',
          letterSpacing: '-0.02em'
        }}
      >
        JihenLine
        <Typography
          component="span"
          sx={{
            fontFamily: TYPOGRAPHY.fontFamily.primary,
            fontWeight: TYPOGRAPHY.fontWeight.medium,
            color: COLORS.text.secondary,
            fontSize: '0.9rem',
            ml: 1
          }}
        >
          Administration
        </Typography>
      </Typography>
    </Box>
  );
}
