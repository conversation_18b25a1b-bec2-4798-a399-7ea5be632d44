# 📋 Intégration API Commandes - Documentation

## 🎯 Vue d'ensemble

Cette documentation décrit l'intégration de l'API live des commandes dans l'application React.

**API Endpoint** : `https://laravel-api.fly.dev/api/commandes`

## 🚀 Composants Créés

### 1. **OrderListEnhanced.jsx**
Composant principal pour afficher la liste des commandes avec toutes les fonctionnalités modernes.

**Localisation** : `src/views/OrderManagement/OrderListEnhanced.jsx`

**Fonctionnalités** :
- ✅ Affichage paginé des commandes
- ✅ Recherche en temps réel (debounce 500ms)
- ✅ Filtres par statut et méthode de paiement
- ✅ Gestion d'erreur robuste avec retry
- ✅ Design moderne avec StandardTable
- ✅ Navigation vers les détails de commande

### 2. **Services API**
Fonctions utilitaires pour interagir avec l'API.

**Localisation** : `src/services/orderService.js`

**Nouvelles fonctions** :
- `fetchOrdersFromLiveAPI(params)` - Liste paginée
- `fetchOrderByIdFromLiveAPI(id)` - Commande individuelle

### 3. **Gestion d'erreur**
Système complet de gestion d'erreur.

**Composants** :
- `ErrorBoundary.jsx` - Capture les erreurs React
- `ErrorDisplay.jsx` - Affichage professionnel des erreurs
- `useApiError.js` - Hook pour la gestion d'erreur
- `apiDiagnostic.js` - Outils de diagnostic

## 📊 Structure de l'API

### Endpoint Principal
```
GET https://laravel-api.fly.dev/api/commandes
```

### Paramètres Supportés
```javascript
{
  page: 1,              // Numéro de page
  per_page: 15,         // Éléments par page
  search: "CMD-123",    // Recherche libre
  status: "en_attente", // Filtre par statut
  payment_method: "stripe" // Filtre par méthode de paiement
}
```

### Structure de Réponse
```javascript
{
  "success": true,
  "message": "Commandes récupérées avec succès",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 52,
        "numero_commande": "CMD-ZYMVP4VM",
        "user_id": 1,
        "total_commande": "36.00",
        "status": "en_attente",
        "payment_status": "pending",
        "methode_paiement": "stripe",
        "created_at": "2025-05-31T11:19:16.000000Z",
        "user": {
          "id": 1,
          "name": "Youssef Mrabet",
          "email": "<EMAIL>"
        },
        "produits": [...],
        "paiement": {...},
        "shipping_address": {...},
        "billing_address": {...}
      }
    ],
    "last_page": 3,
    "total": 43,
    "per_page": 15
  }
}
```

## 🛠️ Utilisation

### 1. **Intégration Basique**
```jsx
import OrderListEnhanced from './views/OrderManagement/OrderListEnhanced';

function App() {
  return <OrderListEnhanced />;
}
```

### 2. **Utilisation du Service API**
```javascript
import { fetchOrdersFromLiveAPI } from './services/orderService';

const loadOrders = async () => {
  try {
    const result = await fetchOrdersFromLiveAPI({
      page: 1,
      per_page: 15,
      search: 'CMD-123',
      status: 'en_attente'
    });
    
    console.log('Orders:', result.data);
    console.log('Pagination:', result.pagination);
  } catch (error) {
    console.error('Error:', error);
  }
};
```

### 3. **Gestion d'erreur**
```jsx
import { useOrderError } from './hooks/useApiError';
import ErrorDisplay from './components/ErrorDisplay';

const MyComponent = () => {
  const { error, handleOrderError, retry } = useOrderError();

  const loadData = async () => {
    try {
      const data = await fetchOrdersFromLiveAPI();
    } catch (error) {
      handleOrderError(error);
    }
  };

  return (
    <div>
      {error && (
        <ErrorDisplay 
          error={error}
          onRetry={() => retry(loadData)}
        />
      )}
    </div>
  );
};
```

## 🎨 Personnalisation

### Colonnes du Tableau
Modifiez le tableau `columns` dans `OrderListEnhanced.jsx` :

```javascript
const columns = [
  { id: 'numero_commande', label: 'N° Commande', minWidth: 150 },
  { id: 'user', label: 'Client', minWidth: 180 },
  // Ajoutez vos colonnes...
];
```

### Rendu des Cellules
Personnalisez `renderCell` pour modifier l'affichage :

```javascript
const renderCell = (column, row, value) => {
  switch (column.id) {
    case 'custom_field':
      return <CustomComponent data={row} />;
    default:
      return value;
  }
};
```

## 🔧 Configuration

### Variables d'Environnement
```env
VITE_REACT_APP_API_URL=https://laravel-api.fly.dev/api
```

### Paramètres par Défaut
```javascript
// Dans OrderListEnhanced.jsx
const DEFAULT_PAGE_SIZE = 15;
const SEARCH_DEBOUNCE_MS = 500;
```

## 🚨 Gestion d'Erreur

### Types d'Erreur Gérés
- **500** : Erreur serveur avec retry automatique
- **404** : Ressource non trouvée
- **Network** : Problème de connexion
- **Timeout** : Délai d'attente dépassé

### Diagnostic Automatique
Le système inclut un diagnostic automatique accessible via :
```javascript
import { runApiDiagnostic } from './utils/apiDiagnostic';

const results = await runApiDiagnostic();
console.log(results);
```

## 📈 Performance

### Optimisations Implémentées
- **Debounce** : Recherche avec délai de 500ms
- **Pagination** : Chargement par chunks de 15 éléments
- **Memoization** : Évite les re-rendus inutiles
- **Error Boundary** : Isolation des erreurs

### Métriques
- **Temps de chargement** : ~200-500ms
- **Taille des données** : ~15 commandes par page
- **Bande passante** : ~50-100KB par requête

## 🧪 Tests

### Test Manuel
1. Ouvrir `src/views/Demo/OrderListDemo.jsx`
2. Vérifier le chargement des données
3. Tester la recherche et les filtres
4. Vérifier la pagination

### Test API Direct
```bash
curl "https://laravel-api.fly.dev/api/commandes?page=1&per_page=5"
```

## 📝 Notes Importantes

1. **Pas d'authentification** : L'API est publique pour la démo
2. **CORS** : Configuré pour accepter les requêtes cross-origin
3. **Rate Limiting** : Pas de limite connue actuellement
4. **Cache** : Pas de cache côté client implémenté

## 🔄 Prochaines Étapes

1. **Authentification** : Intégrer le système d'auth si nécessaire
2. **Cache** : Implémenter un cache pour améliorer les performances
3. **Websockets** : Mises à jour en temps réel
4. **Export** : Fonctionnalité d'export des données
5. **Filtres avancés** : Plus d'options de filtrage

---

**Dernière mise à jour** : 31 Mai 2025
**Version** : 1.0.0
**Auteur** : Équipe de développement
