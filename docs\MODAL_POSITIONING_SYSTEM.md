# Modal Positioning System

## Overview

This system automatically adjusts modal positioning and sizing based on the sidebar state (open/closed) to ensure modals are properly centered and don't overlap with the navbar or sidebar.

## Features

- **Dynamic Positioning**: Modals automatically adjust their position when the sidebar opens or closes
- **Responsive Design**: Different behavior on mobile/tablet vs desktop
- **Smooth Transitions**: Animated transitions when sidebar state changes
- **Professional Styling**: Enhanced visual design with gradients and shadows
- **Accessibility**: Proper focus management and keyboard navigation

## Components

### 1. ProfessionalModal.css
Enhanced CSS with CSS custom properties for dynamic positioning:
- `--current-sidebar-width`: Dynamically updated based on sidebar state
- `--navbar-height`: Fixed navbar height (88px)
- `--modal-margin`: Standard modal margins

### 2. ModalPositionManager.jsx
Global component that manages modal positioning:
- Monitors sidebar state changes
- Updates CSS custom properties
- Handles responsive behavior

### 3. useSidebarModalSync.js (Optional)
Hook for component-level modal positioning (if global manager is not used).

## Usage

### Basic Usage
Simply add the professional modal classes to your Bootstrap modals:

```jsx
<Modal 
  show={showModal} 
  onHide={handleClose}
  className="professional-modal"
  dialogClassName="professional-modal-dialog"
  size="md"
>
  <Modal.Header closeButton className="professional-modal-header bg-primary text-white">
    <Modal.Title>Modal Title</Modal.Title>
  </Modal.Header>
  <Modal.Body className="professional-modal-body">
    Modal content here
  </Modal.Body>
  <Modal.Footer className="professional-modal-footer">
    <Button variant="secondary">Cancel</Button>
    <Button variant="primary">Save</Button>
  </Modal.Footer>
</Modal>
```

### Available Sizes
- `modal-sm`: 420px width
- `modal-md`: 600px width (default)
- `modal-lg`: 800px width
- `modal-xl`: 1000px width (1200px on very wide screens)

### Header Variants
- `bg-primary`: Blue gradient
- `bg-danger`: Red gradient
- `bg-success`: Green gradient
- `bg-warning`: Orange gradient
- `bg-info`: Light blue gradient
- `bg-secondary`: Gray gradient

## Responsive Behavior

### Desktop (≥992px)
- Modals positioned to avoid sidebar overlap
- Dynamic width adjustment based on available space
- Smooth transitions when sidebar toggles

### Tablet (768px - 991px)
- Modals centered with standard responsive widths
- Sidebar typically hidden on these screen sizes

### Mobile (≤767px)
- Full-width modals with minimal margins
- Stacked footer buttons
- Optimized padding and typography

## Technical Details

### CSS Custom Properties
```css
:root {
  --sidebar-width: 260px;        /* Sidebar open width */
  --sidebar-width-mini: 72px;    /* Sidebar closed width */
  --navbar-height: 88px;         /* Fixed navbar height */
  --modal-margin: 1rem;          /* Standard margins */
  --current-sidebar-width: 260px; /* Dynamic value */
}
```

### Positioning Logic
```css
/* Desktop positioning formula */
margin-left: max(1rem, calc((100vw - var(--current-sidebar-width) - var(--modal-width)) / 2 + var(--current-sidebar-width)));
margin-right: max(1rem, calc((100vw - var(--current-sidebar-width) - var(--modal-width)) / 2));
```

## Integration

The system is automatically integrated into the main layout via `ModalPositionManager` component. No additional setup required for new modals - just use the professional modal classes.

## Browser Support

- Modern browsers with CSS custom properties support
- Graceful fallback for older browsers
- Tested on Chrome, Firefox, Safari, and Edge

## Performance

- Minimal JavaScript overhead
- CSS-based positioning for smooth performance
- Efficient event handling with proper cleanup
