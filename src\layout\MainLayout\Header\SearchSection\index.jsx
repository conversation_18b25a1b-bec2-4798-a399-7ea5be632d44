import PropTypes from 'prop-types';
import { forwardRef, useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid2';
import InputAdornment from '@mui/material/InputAdornment';
import OutlinedInput from '@mui/material/OutlinedInput';
import Popper from '@mui/material/Popper';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Divider from '@mui/material/Divider';
import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';

// third party
import PopupState, { bindPopper, bindToggle } from 'material-ui-popup-state';

// project imports
import Transitions from 'ui-component/extended/Transitions';

// Design system
import { COLORS, TYPOGRAPHY } from 'themes/designSystem';

// assets
import {
  IconAdjustmentsHorizontal,
  IconSearch,
  IconX,
  IconShoppingCart,
  IconUsers,
  IconPackage,
  IconFileInvoice,
  IconTags,
  IconCategory,
  IconUser,
  IconCreditCard
} from '@tabler/icons-react';

function HeaderAvatarComponent({ children, ...others }, ref) {
  const theme = useTheme();

  return (
    <Avatar
      ref={ref}
      variant="rounded"
      sx={{
        ...theme.typography.commonAvatar,
        ...theme.typography.mediumAvatar,
        bgcolor: COLORS.primary.light,
        color: COLORS.primary.main,
        cursor: 'pointer',
        transition: 'all .2s ease-in-out',
        '&:hover': {
          bgcolor: COLORS.primary.main,
          color: COLORS.primary.light,
          transform: 'scale(1.05)'
        }
      }}
      {...others}
    >
      {children}
    </Avatar>
  );
}

const HeaderAvatar = forwardRef(HeaderAvatarComponent);

// API Configuration
const API_URL = import.meta.env.REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Search API function
const searchAPI = async (query) => {
  if (!query || query.length < 2) return [];

  try {
    const results = [];

    // Search Orders
    try {
      const ordersResponse = await fetch(`${API_URL}/commandes?search=${encodeURIComponent(query)}&limit=5`);
      if (ordersResponse.ok) {
        const ordersData = await ordersResponse.json();
        const orders = Array.isArray(ordersData) ? ordersData : ordersData.data || [];
        orders.forEach((order) => {
          results.push({
            id: `order-${order.id}`,
            type: 'commande',
            title: `Commande #${order.numero_commande || order.id}`,
            subtitle: `${order.total_commande || order.total || 0}€ - ${order.status || 'En attente'}`,
            icon: IconShoppingCart,
            url: `/app/orders/${order.id}`,
            data: order
          });
        });
      }
    } catch (err) {
      // Silently handle search errors
    }

    // Search Products
    try {
      const productsResponse = await fetch(`${API_URL}/produits?search=${encodeURIComponent(query)}&limit=5`);
      if (productsResponse.ok) {
        const productsData = await productsResponse.json();
        const products = Array.isArray(productsData) ? productsData : productsData.data || [];
        products.forEach((product) => {
          results.push({
            id: `product-${product.id}`,
            type: 'produit',
            title: product.nom_produit || product.nom || 'Produit',
            subtitle: `${product.prix || 0}€ - Réf: ${product.reference_produit || product.reference || 'N/A'}`,
            icon: IconPackage,
            url: `/app/AjoutProduit?edit=${product.id}`,
            data: product
          });
        });
      }
    } catch (err) {
      // Silently handle search errors
    }

    // Search Clients
    try {
      const clientsResponse = await fetch(`${API_URL}/clients?search=${encodeURIComponent(query)}&limit=5`);
      if (clientsResponse.ok) {
        const clientsData = await clientsResponse.json();
        const clients = Array.isArray(clientsData) ? clientsData : clientsData.data || [];
        clients.forEach((client) => {
          results.push({
            id: `client-${client.id}`,
            type: 'client',
            title: client.nom || client.name || 'Client',
            subtitle: client.email || client.telephone || 'Informations limitées',
            icon: IconUser,
            url: `/app/clients/list?search=${encodeURIComponent(client.nom || client.name || client.email || '')}`,
            data: client
          });
        });
      }
    } catch (err) {
      // Silently handle search errors
    }

    // Search Invoices/Payments
    try {
      const paymentsResponse = await fetch(`${API_URL}/paiements?search=${encodeURIComponent(query)}&limit=5`);
      if (paymentsResponse.ok) {
        const paymentsData = await paymentsResponse.json();
        const payments = Array.isArray(paymentsData) ? paymentsData : paymentsData.data || [];
        payments.forEach((payment) => {
          results.push({
            id: `payment-${payment.id}`,
            type: 'facture',
            title: `Facture FAC-${payment.id.toString().padStart(4, '0')}`,
            subtitle: `${payment.montant || payment.amount || 0}€ - ${payment.status || 'Complété'}`,
            icon: IconFileInvoice,
            url: `/app/invoices?search=FAC-${payment.id.toString().padStart(4, '0')}`,
            data: payment
          });
        });
      }
    } catch (err) {
      // Silently handle search errors
    }

    // Add navigation shortcuts if no specific results found
    if (results.length === 0) {
      const lowerQuery = query.toLowerCase();

      // Add navigation shortcuts based on search terms
      if (lowerQuery.includes('commande') || lowerQuery.includes('order')) {
        results.push({
          id: 'nav-orders',
          type: 'navigation',
          title: 'Voir toutes les commandes',
          subtitle: 'Accéder à la liste des commandes',
          icon: IconShoppingCart,
          url: '/app/orders/list',
          data: null
        });
      }

      if (lowerQuery.includes('produit') || lowerQuery.includes('product')) {
        results.push({
          id: 'nav-products',
          type: 'navigation',
          title: 'Ajouter un produit',
          subtitle: 'Accéder à la gestion des produits',
          icon: IconPackage,
          url: '/app/AjoutProduit',
          data: null
        });
      }

      if (lowerQuery.includes('client')) {
        results.push({
          id: 'nav-clients',
          type: 'navigation',
          title: 'Voir tous les clients',
          subtitle: 'Accéder à la liste des clients',
          icon: IconUser,
          url: '/app/clients/list',
          data: null
        });
      }

      if (lowerQuery.includes('facture') || lowerQuery.includes('invoice') || lowerQuery.includes('paiement')) {
        results.push({
          id: 'nav-invoices',
          type: 'navigation',
          title: 'Voir toutes les factures',
          subtitle: 'Accéder à la gestion des factures',
          icon: IconFileInvoice,
          url: '/app/invoices',
          data: null
        });
      }
    }

    return results.slice(0, 15); // Limit total results
  } catch (error) {
    // Silently handle search errors
    return [];
  }
};

// ==============================|| SEARCH INPUT - MOBILE||============================== //

function MobileSearch({ value, setValue, popupState, searchResults, loading, onResultClick }) {
  const theme = useTheme();

  return (
    <Box>
      <OutlinedInput
        id="input-search-header-mobile"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder="Rechercher dans l'administration..."
        startAdornment={
          <InputAdornment position="start">
            <IconSearch stroke={1.5} size="18px" color={COLORS.text.secondary} />
          </InputAdornment>
        }
        endAdornment={
          <InputAdornment position="end">
            {loading && <CircularProgress size={18} sx={{ color: COLORS.primary.main }} />}
            <Box sx={{ ml: 2 }}>
              <Avatar
                variant="rounded"
                sx={{
                  ...theme.typography.commonAvatar,
                  ...theme.typography.mediumAvatar,
                  bgcolor: COLORS.error.light,
                  color: COLORS.error.main,
                  '&:hover': {
                    bgcolor: COLORS.error.main,
                    color: COLORS.error.light
                  }
                }}
                {...bindToggle(popupState)}
              >
                <IconX stroke={1.5} size="18px" />
              </Avatar>
            </Box>
          </InputAdornment>
        }
        aria-describedby="search-helper-text"
        inputProps={{
          'aria-label': 'search',
          sx: {
            bgcolor: 'transparent',
            pl: 0.5,
            fontFamily: TYPOGRAPHY.fontFamily.primary,
            fontSize: TYPOGRAPHY.fontSize.md,
            color: COLORS.text.dark,
            '&::placeholder': {
              color: COLORS.text.secondary,
              opacity: 0.7
            }
          }
        }}
        sx={{
          width: '100%',
          ml: 0.5,
          px: 2,
          bgcolor: 'background.paper',
          borderRadius: 2,
          border: `1px solid ${COLORS.primary.light}30`,
          '&:hover': {
            border: `1px solid ${COLORS.primary.light}60`
          },
          '&.Mui-focused': {
            border: `2px solid ${COLORS.primary.main}`,
            boxShadow: `0 4px 12px ${COLORS.primary.main}20`
          },
          '& .MuiOutlinedInput-notchedOutline': {
            border: 'none'
          }
        }}
      />

      {/* Search Results for Mobile */}
      {value && searchResults.length > 0 && (
        <Card sx={{ mt: 1, maxHeight: 300, overflow: 'auto' }}>
          <List dense>
            {searchResults.map((result, index) => (
              <Box key={result.id}>
                <ListItem button onClick={() => onResultClick(result)} sx={{ py: 1 }}>
                  <ListItemIcon>
                    <result.icon size={20} />
                  </ListItemIcon>
                  <ListItemText
                    primary={result.title}
                    secondary={result.subtitle}
                    primaryTypographyProps={{ fontSize: '0.875rem' }}
                    secondaryTypographyProps={{ fontSize: '0.75rem' }}
                  />
                  <Chip label={result.type} size="small" variant="outlined" sx={{ ml: 1, fontSize: '0.7rem' }} />
                </ListItem>
                {index < searchResults.length - 1 && <Divider />}
              </Box>
            ))}
          </List>
        </Card>
      )}

      {value && !loading && searchResults.length === 0 && value.length >= 2 && (
        <Card sx={{ mt: 1, p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="textSecondary">
            Aucun résultat trouvé pour "{value}"
          </Typography>
        </Card>
      )}
    </Box>
  );
}

// ==============================|| SEARCH INPUT ||============================== //

export default function SearchSection() {
  const [value, setValue] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const navigate = useNavigate();
  const searchRef = useRef(null);

  // Handle click outside to close search results
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Debounced search function
  const debouncedSearch = useCallback(
    (() => {
      let timeoutId;
      return (searchTerm) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(async () => {
          if (searchTerm && searchTerm.length >= 2) {
            setLoading(true);
            try {
              const results = await searchAPI(searchTerm);
              setSearchResults(results);
              setShowResults(true);
            } catch (error) {
              // Silently handle search errors
              setSearchResults([]);
            } finally {
              setLoading(false);
            }
          } else {
            setSearchResults([]);
            setShowResults(false);
          }
        }, 300);
      };
    })(),
    []
  );

  // Handle search input change
  const handleSearchChange = (newValue) => {
    setValue(newValue);
    debouncedSearch(newValue);
  };

  // Handle result click
  const handleResultClick = (result) => {
    setValue('');
    setSearchResults([]);
    setShowResults(false);
    navigate(result.url);
  };

  // Handle clear search
  const handleClearSearch = () => {
    setValue('');
    setSearchResults([]);
    setShowResults(false);
  };

  return (
    <>
      {/* Mobile Search */}
      <Box sx={{ display: { xs: 'block', md: 'none' } }}>
        <PopupState variant="popper" popupId="search-popup-mobile">
          {(popupState) => (
            <>
              <Box sx={{ ml: 2 }}>
                <HeaderAvatar {...bindToggle(popupState)}>
                  <IconSearch stroke={1.5} size="19.2px" />
                </HeaderAvatar>
              </Box>
              <Popper
                {...bindPopper(popupState)}
                transition
                sx={{ zIndex: 1100, width: '99%', top: '-55px !important', px: { xs: 1.25, sm: 1.5 } }}
              >
                {({ TransitionProps }) => (
                  <Transitions type="zoom" {...TransitionProps} sx={{ transformOrigin: 'center left' }}>
                    <Card sx={{ bgcolor: 'background.default', border: 0, boxShadow: 'none' }}>
                      <Box sx={{ p: 2 }}>
                        <Grid container sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
                          <Grid size="grow">
                            <MobileSearch
                              value={value}
                              setValue={handleSearchChange}
                              popupState={popupState}
                              searchResults={searchResults}
                              loading={loading}
                              onResultClick={handleResultClick}
                            />
                          </Grid>
                        </Grid>
                      </Box>
                    </Card>
                  </Transitions>
                )}
              </Popper>
            </>
          )}
        </PopupState>
      </Box>

      {/* Desktop Search */}
      <Box ref={searchRef} sx={{ display: { xs: 'none', md: 'block' }, position: 'relative' }}>
        <OutlinedInput
          id="input-search-header"
          value={value}
          onChange={(e) => handleSearchChange(e.target.value)}
          placeholder="Rechercher dans l'administration..."
          startAdornment={
            <InputAdornment position="start">
              <IconSearch stroke={1.5} size="18px" color={COLORS.text.secondary} />
            </InputAdornment>
          }
          endAdornment={
            <InputAdornment position="end">
              {loading && <CircularProgress size={18} sx={{ mr: 1, color: COLORS.primary.main }} />}
              {value && (
                <HeaderAvatar onClick={handleClearSearch} sx={{ cursor: 'pointer', width: 28, height: 28 }}>
                  <IconX stroke={1.5} size="14px" />
                </HeaderAvatar>
              )}
            </InputAdornment>
          }
          aria-describedby="search-helper-text"
          inputProps={{
            'aria-label': 'search',
            sx: {
              bgcolor: 'transparent',
              pl: 0.5,
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontSize: TYPOGRAPHY.fontSize.md,
              color: COLORS.text.dark,
              '&::placeholder': {
                color: COLORS.text.secondary,
                opacity: 0.7
              }
            }
          }}
          sx={{
            width: { xs: '100%', sm: 300, md: 400, lg: 500 },
            maxWidth: 500,
            mx: 'auto',
            borderRadius: 2,
            bgcolor: 'rgba(255, 255, 255, 0.8)',
            backdropFilter: 'blur(10px)',
            border: `1px solid ${COLORS.primary.light}30`,
            '&:hover': {
              border: `1px solid ${COLORS.primary.light}60`,
              bgcolor: 'rgba(255, 255, 255, 0.9)'
            },
            '&.Mui-focused': {
              border: `2px solid ${COLORS.primary.main}`,
              bgcolor: 'rgba(255, 255, 255, 1)',
              boxShadow: `0 4px 12px ${COLORS.primary.main}20`
            },
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none'
            }
          }}
        />

        {/* Desktop Search Results Dropdown */}
        {showResults && value && (
          <Card
            sx={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              mt: 1,
              zIndex: 1200,
              maxHeight: 400,
              overflow: 'auto',
              width: { xs: '100%', sm: 300, md: 400, lg: 500 },
              maxWidth: 500,
              borderRadius: 2,
              border: `1px solid ${COLORS.primary.light}30`,
              boxShadow: `0 8px 32px ${COLORS.primary.main}15`,
              backdropFilter: 'blur(10px)',
              bgcolor: 'rgba(255, 255, 255, 0.95)'
            }}
          >
            {searchResults.length > 0 ? (
              <List dense sx={{ p: 1 }}>
                {searchResults.map((result, index) => (
                  <Box key={result.id}>
                    <ListItem
                      button
                      onClick={() => handleResultClick(result)}
                      sx={{
                        py: 1.5,
                        px: 2,
                        borderRadius: 1.5,
                        mb: 0.5,
                        '&:hover': {
                          bgcolor: `${COLORS.primary.light}20`,
                          transform: 'translateY(-1px)',
                          boxShadow: `0 4px 12px ${COLORS.primary.main}10`
                        },
                        transition: 'all 0.2s ease-in-out'
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        <result.icon size={20} color={COLORS.primary.main} />
                      </ListItemIcon>
                      <ListItemText
                        primary={result.title}
                        secondary={result.subtitle}
                        primaryTypographyProps={{
                          fontSize: '0.875rem',
                          fontWeight: 500,
                          fontFamily: TYPOGRAPHY.fontFamily.primary,
                          color: COLORS.text.dark
                        }}
                        secondaryTypographyProps={{
                          fontSize: '0.75rem',
                          fontFamily: TYPOGRAPHY.fontFamily.primary,
                          color: COLORS.text.secondary
                        }}
                      />
                      <Chip
                        label={result.type}
                        size="small"
                        variant="outlined"
                        sx={{
                          ml: 1,
                          fontSize: '0.7rem',
                          fontFamily: TYPOGRAPHY.fontFamily.primary,
                          borderColor: COLORS.primary.light,
                          color: COLORS.primary.main,
                          bgcolor: `${COLORS.primary.light}10`
                        }}
                      />
                    </ListItem>
                    {index < searchResults.length - 1 && <Divider sx={{ mx: 2, opacity: 0.3 }} />}
                  </Box>
                ))}
              </List>
            ) : (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    color: COLORS.text.secondary
                  }}
                >
                  Aucun résultat trouvé pour "{value}"
                </Typography>
              </Box>
            )}
          </Card>
        )}
      </Box>
    </>
  );
}

HeaderAvatarComponent.propTypes = { children: PropTypes.node, others: PropTypes.any };

MobileSearch.propTypes = {
  value: PropTypes.string,
  setValue: PropTypes.func,
  popupState: PropTypes.any,
  searchResults: PropTypes.array,
  loading: PropTypes.bool,
  onResultClick: PropTypes.func
};
