import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  Grid,
  Tabs,
  Tab,
  TextField,
  List,
  ListItem,
  ListItemText,
  Divider,
  Paper
} from '@mui/material';
import {
  FaArrowLeft,
  FaEdit,
  FaFileInvoice,
  FaBoxOpen,
  FaHistory,
  FaCommentAlt,
  FaCheck,
  FaTimes,
  FaShippingFast,
  FaCreditCard,
  FaUser,
  FaMapMarkerAlt,
  FaEnvelope,
  FaPhone
} from 'react-icons/fa';
import { fetchOrderById, updateOrderStatus, fetchOrderStatuses, fetchOrderHistory } from '../../services/orderService';
import { useOrderError } from '../../hooks/useApiError';
import ErrorDisplay from '../../components/ErrorDisplay';
import ErrorBoundary from '../../components/ErrorBoundary';
import useProfessionalPrint from '../../hooks/useProfessionalPrint';

// Design system components
import MainCard from 'ui-component/cards/MainCard';
import StandardButton from 'ui-component/buttons/StandardButton';
import StandardTable from 'ui-component/tables/StandardTable';
import StandardCard from 'ui-component/cards/StandardCard';

// Design system
import { COLORS, TYPOGRAPHY } from 'themes/designSystem';

const OrderDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // State
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const { error, isRetrying, retryCount, handleOrderError, retry, clearError } = useOrderError();
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('details');
  const [orderStatuses, setOrderStatuses] = useState([]);
  const [orderHistory, setOrderHistory] = useState([]);
  const [statusForm, setStatusForm] = useState({
    status: '',
    notes: ''
  });
  const [submitting, setSubmitting] = useState(false);

  // Professional print hook
  const { printOrder, isPrinting } = useProfessionalPrint();

  // Load order data
  const loadOrder = async () => {
    setLoading(true);
    clearError();
    try {
      const data = await fetchOrderById(id);
      setOrder(data);
      setStatusForm({
        status: data.status,
        notes: ''
      });
    } catch (e) {
      handleOrderError(e, id);
    }
    setLoading(false);
  };

  // Load order statuses
  const loadOrderStatuses = async () => {
    try {
      const data = await fetchOrderStatuses();
      setOrderStatuses(data);
    } catch (e) {
      // Silent error handling
    }
  };

  // Load order history
  const loadOrderHistory = async () => {
    try {
      const data = await fetchOrderHistory(id);
      setOrderHistory(data);
    } catch (e) {
      // Silent error handling
    }
  };

  // Initial data load
  useEffect(() => {
    if (id) {
      loadOrder();
      loadOrderStatuses();
      loadOrderHistory();
    }
  }, [id]);

  // Handle status update
  const handleStatusUpdate = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');
    setSuccess('');

    try {
      await updateOrderStatus(id, statusForm.status, statusForm.notes);
      setSuccess('Statut mis à jour avec succès');
      loadOrder();
      loadOrderHistory();
    } catch (e) {
      setError(`Erreur lors de la mise à jour du statut: ${e.message}`);
    }

    setSubmitting(false);
  };

  // Get status badge variant
  const getStatusVariant = (status) => {
    const statusColorMap = {
      en_attente: 'warning',
      confirmee: 'info',
      en_preparation: 'primary',
      expediee: 'primary',
      livree: 'success',
      annulee: 'danger',
      remboursee: 'secondary',
      retournee: 'secondary'
    };

    return statusColorMap[status] || 'secondary';
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  // Format price
  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  };

  if (loading) {
    return (
      <MainCard>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
          <CircularProgress size={40} sx={{ color: COLORS.primary.main }} />
          <Typography
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              mt: 2
            }}
          >
            Chargement des détails de la commande...
          </Typography>
        </Box>
      </MainCard>
    );
  }

  if (error && !order) {
    return (
      <ErrorBoundary>
        <MainCard>
          <Box sx={{ py: 4 }}>
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
            <StandardButton variant="outline" onClick={() => navigate('/app/orders')} startIcon={<FaArrowLeft />}>
              Retour aux commandes
            </StandardButton>
          </Box>
        </MainCard>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <MainCard>
        <Box sx={{ width: '100%' }}>
          {/* Breadcrumb - Design System Style */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                fontSize: TYPOGRAPHY.fontSize.sm
              }}
            >
              Accueil &gt; Commandes &gt; Détail #{order?.numero_commande || order?.order_number || `CMD-${order?.id}`}
            </Typography>
          </Box>

          {/* Header - Design System Style */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
            <Box>
              <Typography
                variant="h3"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  fontWeight: TYPOGRAPHY.fontWeight.bold,
                  color: COLORS.text.dark,
                  mb: 1
                }}
              >
                Commande #{order?.numero_commande || order?.order_number || `CMD-${order?.id}`}
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  color: COLORS.text.secondary,
                  fontSize: TYPOGRAPHY.fontSize.md
                }}
              >
                Détails de la commande
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <StandardButton variant="outline" onClick={() => navigate('/app/orders')} startIcon={<FaArrowLeft />}>
                Retour
              </StandardButton>
              <StandardButton
                variant="primary"
                onClick={() => order && printOrder(order)}
                startIcon={<FaFileInvoice />}
                disabled={!order || isPrinting}
                loading={isPrinting}
                loadingText="Impression..."
              >
                Imprimer
              </StandardButton>
            </Box>
          </Box>

          {/* Enhanced Error Display */}
          {error && (
            <Box sx={{ mb: 3 }}>
              <Alert severity="error">{error}</Alert>
            </Box>
          )}
          {success && (
            <Box sx={{ mb: 3 }}>
              <Alert severity="success">{success}</Alert>
            </Box>
          )}

          {/* Main Content */}
          <Grid container spacing={3}>
            {/* Order Details */}
            <Grid item xs={12} lg={8}>
              <StandardCard title="Détails de la commande">
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                  <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
                    <Tab label="Détails" value="details" />
                    <Tab label="Historique" value="history" />
                    <Tab label="Statut" value="status" />
                  </Tabs>
                </Box>

                {/* Tab Content */}
                {activeTab === 'details' && (
                  <Box>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <Typography
                          variant="h6"
                          sx={{
                            fontFamily: TYPOGRAPHY.fontFamily.primary,
                            fontWeight: TYPOGRAPHY.fontWeight.semibold,
                            color: COLORS.text.dark,
                            mb: 2
                          }}
                        >
                          Informations de la commande
                        </Typography>
                        <List>
                          <ListItem>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      color: COLORS.text.secondary
                                    }}
                                  >
                                    Numéro de commande
                                  </Typography>
                                  <Typography
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      fontWeight: TYPOGRAPHY.fontWeight.medium,
                                      color: COLORS.text.dark
                                    }}
                                  >
                                    {order.numero_commande || order.order_number || `CMD-${order.id}`}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      color: COLORS.text.secondary
                                    }}
                                  >
                                    Date de commande
                                  </Typography>
                                  <Typography
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      color: COLORS.text.dark
                                    }}
                                  >
                                    {formatDate(order.created_at)}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      color: COLORS.text.secondary
                                    }}
                                  >
                                    Statut
                                  </Typography>
                                  <Chip
                                    label={order._original?.status || order.status}
                                    color="primary"
                                    size="small"
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      fontWeight: TYPOGRAPHY.fontWeight.medium
                                    }}
                                  />
                                </Box>
                              }
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      color: COLORS.text.secondary
                                    }}
                                  >
                                    Méthode de paiement
                                  </Typography>
                                  <Typography
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      color: COLORS.text.dark
                                    }}
                                  >
                                    {order.paiement?.methode || order.methode_paiement || order.payment_method || 'Non spécifiée'}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      color: COLORS.text.secondary
                                    }}
                                  >
                                    Total
                                  </Typography>
                                  <Typography
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      fontWeight: TYPOGRAPHY.fontWeight.bold,
                                      color: COLORS.text.dark
                                    }}
                                  >
                                    {formatPrice(order.total_commande || order.total || 0)}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                        </List>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography
                          variant="h6"
                          sx={{
                            fontFamily: TYPOGRAPHY.fontFamily.primary,
                            fontWeight: TYPOGRAPHY.fontWeight.semibold,
                            color: COLORS.text.dark,
                            mb: 2
                          }}
                        >
                          Adresse de livraison
                        </Typography>
                        <List>
                          <ListItem>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                                  <FaMapMarkerAlt style={{ marginRight: 8, marginTop: 4, color: COLORS.text.secondary }} />
                                  <Box>
                                    <Typography
                                      sx={{
                                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                                        color: COLORS.text.dark,
                                        mb: 0.5
                                      }}
                                    >
                                      {order.shipping_address?.street || order.shipping_address_line1 || 'Non spécifiée'}
                                    </Typography>
                                    <Typography
                                      sx={{
                                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                                        color: COLORS.text.dark,
                                        mb: 0.5
                                      }}
                                    >
                                      {order.shipping_address?.postal_code || order.shipping_postal_code || ''}{' '}
                                      {order.shipping_address?.city || order.shipping_city || ''}
                                    </Typography>
                                    <Typography
                                      sx={{
                                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                                        color: COLORS.text.dark
                                      }}
                                    >
                                      {order.shipping_address?.country || order.shipping_country || 'Tunisie'}
                                    </Typography>
                                  </Box>
                                </Box>
                              }
                            />
                          </ListItem>
                        </List>

                        <Typography
                          variant="h6"
                          sx={{
                            fontFamily: TYPOGRAPHY.fontFamily.primary,
                            fontWeight: TYPOGRAPHY.fontWeight.semibold,
                            color: COLORS.text.dark,
                            mb: 2,
                            mt: 3
                          }}
                        >
                          Adresse de facturation
                        </Typography>
                        <List>
                          <ListItem>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                                  <FaMapMarkerAlt style={{ marginRight: 8, marginTop: 4, color: COLORS.text.secondary }} />
                                  <Box>
                                    <Typography
                                      sx={{
                                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                                        color: COLORS.text.dark,
                                        mb: 0.5
                                      }}
                                    >
                                      {order.billing_address?.street ||
                                        order.shipping_address_line1 ||
                                        "Identique à l'adresse de livraison"}
                                    </Typography>
                                    <Typography
                                      sx={{
                                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                                        color: COLORS.text.dark,
                                        mb: 0.5
                                      }}
                                    >
                                      {order.billing_address?.postal_code || order.shipping_postal_code || ''}{' '}
                                      {order.billing_address?.city || order.shipping_city || ''}
                                    </Typography>
                                    <Typography
                                      sx={{
                                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                                        color: COLORS.text.dark
                                      }}
                                    >
                                      {order.billing_address?.country || order.shipping_country || 'Tunisie'}
                                    </Typography>
                                  </Box>
                                </Box>
                              }
                            />
                          </ListItem>
                        </List>
                      </Grid>
                    </Grid>

                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.semibold,
                        color: COLORS.text.dark,
                        mb: 2,
                        mt: 3
                      }}
                    >
                      Produits commandés
                    </Typography>
                    <StandardTable
                      columns={[
                        { id: 'produit', label: 'Produit', minWidth: 200 },
                        { id: 'quantite', label: 'Quantité', width: 100, align: 'center' },
                        { id: 'prix_unitaire', label: 'Prix unitaire', width: 120, align: 'right' },
                        { id: 'total', label: 'Total', width: 120, align: 'right' }
                      ]}
                      data={order.produits || order.items || []}
                      renderCell={(column, row) => {
                        switch (column.id) {
                          case 'produit':
                            return (
                              <Typography
                                sx={{
                                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                                  color: COLORS.text.dark
                                }}
                              >
                                {row.nom_produit || row.product_name || 'Produit'}
                              </Typography>
                            );
                          case 'quantite':
                            return (
                              <Typography
                                sx={{
                                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                                  color: COLORS.text.dark
                                }}
                              >
                                {row.pivot?.quantite || row.quantity || 1}
                              </Typography>
                            );
                          case 'prix_unitaire':
                            return (
                              <Typography
                                sx={{
                                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                                  color: COLORS.text.dark
                                }}
                              >
                                {formatPrice(row.pivot?.prix_unitaire || row.unit_price || 0)}
                              </Typography>
                            );
                          case 'total':
                            return (
                              <Typography
                                sx={{
                                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                                  color: COLORS.text.dark
                                }}
                              >
                                {formatPrice(row.pivot?.total_ligne || row.total_price || 0)}
                              </Typography>
                            );
                          default:
                            return row[column.id];
                        }
                      }}
                      emptyMessage="Aucun produit dans cette commande"
                    />

                    {/* Total */}
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        mt: 2,
                        p: 2,
                        backgroundColor: COLORS.primary.light + '10',
                        borderRadius: 1
                      }}
                    >
                      <Typography
                        sx={{
                          fontFamily: TYPOGRAPHY.fontFamily.primary,
                          fontWeight: TYPOGRAPHY.fontWeight.bold,
                          color: COLORS.text.dark,
                          fontSize: TYPOGRAPHY.fontSize.lg
                        }}
                      >
                        Total: {formatPrice(order.total_commande || order.total || 0)}
                      </Typography>
                    </Box>
                  </Box>
                )}

                {/* History Tab */}
                {activeTab === 'history' && (
                  <Box>
                    <List>
                      {orderHistory.map((history) => (
                        <ListItem key={history.id}>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Chip
                                    label={history.status}
                                    color="primary"
                                    size="small"
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      mr: 1
                                    }}
                                  />
                                  <Typography
                                    sx={{
                                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                                      color: COLORS.text.dark
                                    }}
                                  >
                                    {history.description}
                                  </Typography>
                                </Box>
                                <Typography
                                  variant="caption"
                                  sx={{
                                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                                    color: COLORS.text.secondary
                                  }}
                                >
                                  {formatDate(history.created_at)}
                                </Typography>
                              </Box>
                            }
                            secondary={
                              history.notes && (
                                <Typography
                                  sx={{
                                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                                    color: COLORS.text.secondary,
                                    mt: 1,
                                    fontSize: TYPOGRAPHY.fontSize.sm
                                  }}
                                >
                                  {history.notes}
                                </Typography>
                              )
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}
              </StandardCard>
            </Grid>

            {/* Status Update */}
            <Grid item xs={12} lg={4}>
              <StandardCard title="Mettre à jour le statut">
                <Box component="form" onSubmit={handleStatusUpdate}>
                  <TextField
                    select
                    fullWidth
                    label="Nouveau statut"
                    value={statusForm.status}
                    onChange={(e) => setStatusForm({ ...statusForm, status: e.target.value })}
                    required
                    sx={{
                      mb: 3,
                      '& .MuiInputLabel-root': {
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      },
                      '& .MuiInputBase-input': {
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      }
                    }}
                  >
                    <option value="">Sélectionner un statut</option>
                    {orderStatuses.map((status) => (
                      <option key={status.id} value={status.value}>
                        {status.name}
                      </option>
                    ))}
                  </TextField>

                  <TextField
                    fullWidth
                    label="Notes"
                    multiline
                    rows={3}
                    value={statusForm.notes}
                    onChange={(e) => setStatusForm({ ...statusForm, notes: e.target.value })}
                    placeholder="Ajouter des notes sur la mise à jour du statut..."
                    sx={{
                      mb: 3,
                      '& .MuiInputLabel-root': {
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      },
                      '& .MuiInputBase-input': {
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      }
                    }}
                  />

                  <StandardButton
                    variant="primary"
                    type="submit"
                    fullWidth
                    disabled={submitting || !statusForm.status}
                    loading={submitting}
                    loadingText="Mise à jour..."
                  >
                    Mettre à jour le statut
                  </StandardButton>
                </Box>
              </StandardCard>
            </Grid>
          </Grid>
        </Box>
      </MainCard>
    </ErrorBoundary>
  );
};

export default OrderDetail;
