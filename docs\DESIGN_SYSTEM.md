# Design System Documentation

## Overview

This document outlines the standardized design system implemented across the application to ensure consistency in colors, typography, buttons, tables, and other UI components for production readiness.

## 🎨 Design Principles

- **Consistency**: All components follow the same design patterns
- **Accessibility**: Colors and typography meet accessibility standards
- **Scalability**: Easy to maintain and extend
- **Performance**: Optimized for production use

## 📁 File Structure

```
src/
├── themes/
│   ├── designSystem.js          # Central design constants
│   ├── compStyleOverride.jsx    # Material-UI theme overrides
│   └── palette.jsx              # Color palette
├── components/
│   ├── StandardTable.jsx        # Standardized table component
│   ├── StandardButton.jsx       # Standardized button component
│   └── StandardCard.jsx         # Standardized card component
├── assets/scss/
│   ├── standardized-styles.scss # Global CSS utilities
│   └── style.scss               # Main stylesheet
└── views/utilities/
    └── DesignSystemDemo.jsx     # Demo page showing all components
```

## 🎨 Colors

### Primary Colors
- **Primary Main**: `#2196f3` (Blue)
- **Primary Light**: `#e3f2fd` (Light Blue)
- **Primary Dark**: `#1e88e5` (Dark Blue)

### Status Colors
- **Success**: `#00e676` (Green)
- **Error**: `#f44336` (Red)
- **Warning**: `#ffe57f` (Yellow)

### Grey Scale
- **Grey 50**: `#f8fafc` (Lightest)
- **Grey 100**: `#eef2f6`
- **Grey 200**: `#e3e8ef`
- **Grey 500**: `#697586`
- **Grey 700**: `#364152`
- **Grey 900**: `#121926` (Darkest)

### Usage
```javascript
import { COLORS } from '../themes/designSystem';

// In component
sx={{ color: COLORS.primary.main }}
```

## 📝 Typography

### Font Families
- **Primary**: `'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`
- **Secondary**: `'Poppins', sans-serif`
- **Monospace**: `'Roboto Mono', monospace`

### Font Sizes
- **xs**: `0.75rem` (12px)
- **sm**: `0.875rem` (14px)
- **base**: `1rem` (16px)
- **lg**: `1.125rem` (18px)
- **xl**: `1.25rem` (20px)

### Font Weights
- **Normal**: 400
- **Medium**: 500
- **Semibold**: 600
- **Bold**: 700

### Usage
```javascript
import { TYPOGRAPHY } from '../themes/designSystem';

// In component
sx={{ 
  fontFamily: TYPOGRAPHY.fontFamily.primary,
  fontSize: TYPOGRAPHY.fontSize.sm,
  fontWeight: TYPOGRAPHY.fontWeight.medium
}}
```

## 🔘 Buttons

### StandardButton Component

```javascript
import StandardButton from '../components/StandardButton';

// Basic usage
<StandardButton variant="primary" onClick={handleClick}>
  Click Me
</StandardButton>

// With icons and loading state
<StandardButton 
  variant="success" 
  size="large"
  loading={isLoading}
  startIcon={<IconPlus />}
  onClick={handleSubmit}
>
  Add Item
</StandardButton>
```

### Variants
- `primary` - Blue background
- `secondary` - Purple background
- `success` - Green background
- `error` - Red background
- `warning` - Yellow background
- `outline` - Transparent with border

### Sizes
- `small` - 32px height
- `medium` - 40px height (default)
- `large` - 48px height

## 📊 Tables

### StandardTable Component

Based on the client page table styling for consistency.

```javascript
import StandardTable from '../components/StandardTable';

const columns = [
  { id: 'id', label: 'ID', minWidth: 70 },
  { id: 'name', label: 'Name', minWidth: 150 },
  { id: 'email', label: 'Email', minWidth: 200 }
];

const data = [
  { id: 1, name: 'John Doe', email: '<EMAIL>' }
];

<StandardTable
  columns={columns}
  data={data}
  loading={false}
  error={null}
  hover={true}
  pagination={{
    page: 1,
    totalPages: 5,
    onPageChange: handlePageChange
  }}
  renderCell={(column, row, value) => {
    // Custom cell rendering
    if (column.id === 'status') {
      return <Chip label={value} color="success" />;
    }
    return value;
  }}
/>
```

### Features
- Consistent styling with client page
- Loading and error states
- Custom cell rendering
- Pagination support
- Hover effects
- Responsive design

## 🃏 Cards

### StandardCard Component

```javascript
import StandardCard from '../components/StandardCard';

<StandardCard 
  title="Card Title"
  subtitle="Optional subtitle"
  action={<Button>Action</Button>}
  size="medium"
  shadow={true}
>
  Card content goes here
</StandardCard>
```

### Props
- `title` - Card title
- `subtitle` - Optional subtitle
- `action` - Action component in header
- `size` - `small`, `medium`, `large`
- `shadow` - Enable/disable shadow
- `divider` - Show divider after header

## 🎨 CSS Utility Classes

### Typography
```css
.std-font-primary     /* Inter font family */
.std-font-secondary   /* Poppins font family */
.std-text-sm         /* Small text size */
.std-text-lg         /* Large text size */
.std-font-medium     /* Medium font weight */
.std-font-bold       /* Bold font weight */
```

### Colors
```css
.std-text-primary    /* Primary text color */
.std-text-secondary  /* Secondary text color */
.std-bg-primary      /* Primary background */
.std-bg-light        /* Light background */
```

### Spacing
```css
.std-p-md           /* Medium padding */
.std-m-lg           /* Large margin */
```

### Buttons
```css
.std-btn             /* Base button class */
.std-btn-primary     /* Primary button */
.std-btn-outline     /* Outline button */
```

## 🚀 Migration Guide

### From Bootstrap to Standardized Components

1. **Replace Bootstrap Tables**:
   ```javascript
   // Before
   <Table striped bordered hover>
   
   // After
   <StandardTable columns={columns} data={data} hover={true} />
   ```

2. **Replace Bootstrap Buttons**:
   ```javascript
   // Before
   <Button variant="primary">Click Me</Button>
   
   // After
   <StandardButton variant="primary">Click Me</StandardButton>
   ```

3. **Replace Bootstrap Cards**:
   ```javascript
   // Before
   <Card>
     <Card.Header>Title</Card.Header>
     <Card.Body>Content</Card.Body>
   </Card>
   
   // After
   <StandardCard title="Title">Content</StandardCard>
   ```

### Update Existing Components

1. Import design system constants:
   ```javascript
   import { COLORS, TYPOGRAPHY, SPACING } from '../themes/designSystem';
   ```

2. Replace hardcoded values:
   ```javascript
   // Before
   sx={{ color: '#2196f3', fontSize: '14px' }}
   
   // After
   sx={{ color: COLORS.primary.main, fontSize: TYPOGRAPHY.fontSize.sm }}
   ```

3. Use standardized components:
   ```javascript
   import StandardButton from '../components/StandardButton';
   import StandardTable from '../components/StandardTable';
   import StandardCard from '../components/StandardCard';
   ```

## 📋 Best Practices

1. **Always use standardized components** instead of creating custom ones
2. **Import design constants** from `designSystem.js` instead of hardcoding values
3. **Use CSS utility classes** for quick styling
4. **Follow the client page table style** for all data tables
5. **Test components** in the DesignSystemDemo page before using
6. **Maintain consistency** across all pages

## 🔧 Development

### Adding New Components

1. Create component in `src/components/`
2. Follow existing patterns from StandardButton/StandardTable
3. Use design system constants
4. Add to DesignSystemDemo page
5. Document usage in this file

### Updating Design System

1. Modify constants in `src/themes/designSystem.js`
2. Update theme overrides in `src/themes/compStyleOverride.jsx`
3. Test changes in DesignSystemDemo page
4. Update documentation

## 📖 Demo Page

Visit `/design-system-demo` to see all standardized components in action and test different variants, sizes, and states.

## 🎯 Production Checklist

- [ ] All pages use standardized components
- [ ] No hardcoded colors or fonts
- [ ] Consistent table styling across all pages
- [ ] All buttons use StandardButton component
- [ ] Design system constants are imported correctly
- [ ] CSS utility classes are used where appropriate
- [ ] Components are tested in different states
- [ ] Documentation is up to date
