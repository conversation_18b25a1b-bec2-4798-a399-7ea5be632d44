import { useState } from 'react';

const useProfessionalPrint = () => {
  const [isPrinting, setIsPrinting] = useState(false);

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatPrice = (price) => {
    if (!price) return '0.000 DT';
    return `${parseFloat(price).toFixed(3)} DT`;
  };

  const printOrder = (orderData) => {
    setIsPrinting(true);
    
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Commande ${orderData.numero_commande || orderData.order_number || `CMD-${orderData.id}`} - JihenLine</title>
        <meta charset="utf-8">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 20px;
          }
          
          .print-container {
            max-width: 800px;
            margin: 0 auto;
          }
          
          .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 3px solid #1976d2;
            padding-bottom: 20px;
          }
          
          .company-info h1 {
            font-size: 28px;
            color: #1976d2;
            margin-bottom: 5px;
            font-weight: bold;
          }
          
          .company-info p {
            margin: 2px 0;
            color: #666;
          }
          
          .document-info {
            text-align: right;
          }
          
          .document-info h2 {
            font-size: 24px;
            color: #1976d2;
            margin-bottom: 10px;
          }
          
          .document-info p {
            margin: 3px 0;
          }
          
          .section {
            margin: 25px 0;
          }
          
          .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 5px;
          }
          
          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
          }
          
          .info-item {
            margin-bottom: 8px;
          }
          
          .info-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            min-width: 120px;
          }
          
          .info-value {
            color: #333;
          }
          
          .products-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
          }
          
          .products-table th,
          .products-table td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
          }
          
          .products-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #333;
          }
          
          .products-table .text-right {
            text-align: right;
          }
          
          .products-table .text-center {
            text-align: center;
          }
          
          .total-section {
            margin-top: 20px;
            text-align: right;
          }
          
          .total-row {
            display: flex;
            justify-content: flex-end;
            margin: 5px 0;
          }
          
          .total-label {
            min-width: 150px;
            font-weight: bold;
            padding: 5px 10px;
          }
          
          .total-value {
            min-width: 100px;
            padding: 5px 10px;
            text-align: right;
          }
          
          .grand-total {
            background-color: #1976d2;
            color: white;
            font-size: 16px;
            font-weight: bold;
          }
          
          .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
            color: #666;
            font-size: 11px;
          }
          
          .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            background-color: #e3f2fd;
            color: #1976d2;
          }
          
          .address-section {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
          }
          
          @media print {
            body {
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
              padding: 0;
            }
            
            .print-container {
              max-width: none;
              margin: 0;
            }
            
            .header {
              page-break-after: avoid;
            }
            
            .products-table {
              page-break-inside: avoid;
            }
            
            .footer {
              page-break-before: avoid;
            }
          }
        </style>
      </head>
      <body>
        <div class="print-container">
          <!-- Header -->
          <div class="header">
            <div class="company-info">
              <h1>JihenLine</h1>
              <p>Plateforme E-commerce</p>
              <p>Tunis, Tunisie</p>
              <p>Email: <EMAIL></p>
              <p>Tél: +216 XX XXX XXX</p>
            </div>
            <div class="document-info">
              <h2>COMMANDE</h2>
              <p><strong>N°:</strong> ${orderData.numero_commande || orderData.order_number || `CMD-${orderData.id}`}</p>
              <p><strong>Date:</strong> ${formatDate(orderData.created_at)}</p>
              <p><strong>Statut:</strong> <span class="status-badge">${orderData.status || 'N/A'}</span></p>
            </div>
          </div>

          <!-- Customer Info -->
          <div class="section">
            <div class="section-title">Informations Client</div>
            <div class="info-grid">
              <div>
                <div class="info-item">
                  <span class="info-label">Nom:</span>
                  <span class="info-value">${orderData.user?.name || orderData.customer_name || 'N/A'}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Email:</span>
                  <span class="info-value">${orderData.user?.email || orderData.customer_email || 'N/A'}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Téléphone:</span>
                  <span class="info-value">${orderData.user?.phone || orderData.customer_phone || 'N/A'}</span>
                </div>
              </div>
              <div>
                <div class="info-item">
                  <span class="info-label">Méthode de paiement:</span>
                  <span class="info-value">${orderData.methode_paiement || orderData.payment_method || 'N/A'}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Date de commande:</span>
                  <span class="info-value">${formatDate(orderData.created_at)}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Shipping Address -->
          ${(orderData.shipping_address || orderData.shipping_address_line1) ? `
          <div class="section">
            <div class="section-title">Adresse de Livraison</div>
            <div class="address-section">
              <div>${orderData.shipping_address?.street || orderData.shipping_address_line1 || ''}</div>
              <div>${orderData.shipping_address?.postal_code || orderData.shipping_postal_code || ''} ${orderData.shipping_address?.city || orderData.shipping_city || ''}</div>
              <div>${orderData.shipping_address?.country || orderData.shipping_country || 'Tunisie'}</div>
            </div>
          </div>
          ` : ''}

          <!-- Products -->
          <div class="section">
            <div class="section-title">Produits Commandés</div>
            <table class="products-table">
              <thead>
                <tr>
                  <th>Produit</th>
                  <th class="text-center">Quantité</th>
                  <th class="text-right">Prix Unitaire</th>
                  <th class="text-right">Total</th>
                </tr>
              </thead>
              <tbody>
                ${(orderData.produits || orderData.items || []).map((produit, index) => `
                  <tr>
                    <td>${produit.nom_produit || produit.product_name || 'Produit'}</td>
                    <td class="text-center">${produit.pivot?.quantite || produit.quantity || 1}</td>
                    <td class="text-right">${formatPrice(produit.pivot?.prix_unitaire || produit.unit_price || 0)}</td>
                    <td class="text-right">${formatPrice(produit.pivot?.total_ligne || produit.total_price || 0)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <div class="total-section">
              <div class="total-row grand-total">
                <div class="total-label">TOTAL</div>
                <div class="total-value">${formatPrice(orderData.total_commande || orderData.total || 0)}</div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>Merci pour votre confiance - JihenLine</p>
            <p>Document généré le ${formatDate(new Date().toISOString())}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
        setIsPrinting(false);
      }, 500);
    } else {
      setIsPrinting(false);
    }
  };

  const printInvoice = (invoiceData) => {
    setIsPrinting(true);
    
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Facture ${invoiceData.invoice_number || `FAC-${invoiceData.id}`} - JihenLine</title>
        <meta charset="utf-8">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 20px;
          }
          
          .print-container {
            max-width: 800px;
            margin: 0 auto;
          }
          
          .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 3px solid #1976d2;
            padding-bottom: 20px;
          }
          
          .company-info h1 {
            font-size: 28px;
            color: #1976d2;
            margin-bottom: 5px;
            font-weight: bold;
          }
          
          .company-info p {
            margin: 2px 0;
            color: #666;
          }
          
          .document-info {
            text-align: right;
          }
          
          .document-info h2 {
            font-size: 24px;
            color: #1976d2;
            margin-bottom: 10px;
          }
          
          .section {
            margin: 25px 0;
          }
          
          .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 5px;
          }
          
          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
          }
          
          .info-item {
            margin-bottom: 8px;
          }
          
          .info-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            min-width: 120px;
          }
          
          .total-section {
            margin-top: 20px;
            text-align: right;
          }
          
          .total-row {
            display: flex;
            justify-content: flex-end;
            margin: 5px 0;
          }
          
          .total-label {
            min-width: 150px;
            font-weight: bold;
            padding: 5px 10px;
          }
          
          .total-value {
            min-width: 100px;
            padding: 5px 10px;
            text-align: right;
          }
          
          .grand-total {
            background-color: #1976d2;
            color: white;
            font-size: 16px;
            font-weight: bold;
          }
          
          .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
            color: #666;
            font-size: 11px;
          }
          
          .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            background-color: #e8f5e8;
            color: #2e7d32;
          }
          
          @media print {
            body {
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
              padding: 0;
            }
            
            .print-container {
              max-width: none;
              margin: 0;
            }
          }
        </style>
      </head>
      <body>
        <div class="print-container">
          <!-- Header -->
          <div class="header">
            <div class="company-info">
              <h1>JihenLine</h1>
              <p>Plateforme E-commerce</p>
              <p>Tunis, Tunisie</p>
              <p>Email: <EMAIL></p>
              <p>Tél: +216 XX XXX XXX</p>
              <p>TVA: TN123456789</p>
              <p>RC: B123456789</p>
            </div>
            <div class="document-info">
              <h2>FACTURE</h2>
              <p><strong>N°:</strong> ${invoiceData.invoice_number || `FAC-${invoiceData.id}`}</p>
              <p><strong>Date:</strong> ${formatDate(invoiceData.invoice_date || invoiceData.created_at)}</p>
              <p><strong>Statut:</strong> <span class="status-badge">${invoiceData.status || 'Payée'}</span></p>
            </div>
          </div>

          <!-- Invoice Info -->
          <div class="section">
            <div class="section-title">Informations Facture</div>
            <div class="info-grid">
              <div>
                <div class="info-item">
                  <span class="info-label">Commande N°:</span>
                  <span class="info-value">${invoiceData.order?.numero_commande || invoiceData.order?.order_number || `CMD-${invoiceData.order?.id}` || 'N/A'}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Client:</span>
                  <span class="info-value">${invoiceData.order?.user?.name || invoiceData.order?.customer_name || 'N/A'}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Email:</span>
                  <span class="info-value">${invoiceData.order?.user?.email || invoiceData.order?.customer_email || 'N/A'}</span>
                </div>
              </div>
              <div>
                <div class="info-item">
                  <span class="info-label">Méthode de paiement:</span>
                  <span class="info-value">${invoiceData.methode_paiement || 'N/A'}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Date de paiement:</span>
                  <span class="info-value">${formatDate(invoiceData.created_at)}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Payment Details -->
          <div class="section">
            <div class="section-title">Détails du Paiement</div>
            <div class="total-section">
              <div class="total-row">
                <div class="total-label">Montant HT:</div>
                <div class="total-value">${formatPrice((invoiceData.amount || invoiceData.montant || 0) / 1.19)}</div>
              </div>
              <div class="total-row">
                <div class="total-label">TVA (19%):</div>
                <div class="total-value">${formatPrice((invoiceData.amount || invoiceData.montant || 0) * 0.19 / 1.19)}</div>
              </div>
              <div class="total-row grand-total">
                <div class="total-label">TOTAL TTC</div>
                <div class="total-value">${formatPrice(invoiceData.amount || invoiceData.montant || 0)}</div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>Merci pour votre confiance - JihenLine</p>
            <p>Facture générée le ${formatDate(new Date().toISOString())}</p>
            <p>TVA: TN123456789 - RC: B123456789</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
        setIsPrinting(false);
      }, 500);
    } else {
      setIsPrinting(false);
    }
  };

  return {
    printOrder,
    printInvoice,
    isPrinting
  };
};

export default useProfessionalPrint;
