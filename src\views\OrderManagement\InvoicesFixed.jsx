import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Modal,
  Button,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Print as PrintIcon,
  Close as CloseIcon,
  Receipt as ReceiptIcon,
  Person as PersonIcon,
  Payment as PaymentIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

// Standardized components
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';
import StandardButton from '../../components/StandardButton';
import MainCard from '../../ui-component/cards/MainCard';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';

const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

const InvoicesFixed = () => {
  // States
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(15);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [totalInvoices, setTotalInvoices] = useState(0);

  // Load invoices on component mount
  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch payments which represent our invoices
      const paymentsResponse = await fetch(`${API_URL}/paiements`);
      if (!paymentsResponse.ok) {
        throw new Error('Erreur lors du chargement des paiements');
      }

      const paymentsData = await paymentsResponse.json();
      const payments = Array.isArray(paymentsData) ? paymentsData : paymentsData.data || [];

      console.log('💳 Payments API response:', paymentsData);

      // For each payment, fetch the related order to get complete invoice data
      const invoicesWithOrders = await Promise.all(
        payments.map(async (payment) => {
          try {
            const orderResponse = await fetch(`${API_URL}/commandes/${payment.commande_id}?with=user,client,produits`);

            if (orderResponse.ok) {
              const orderResponseData = await orderResponse.json();
              const orderData = orderResponseData.data || orderResponseData;

              const finalPaymentMethod = orderData.methode_paiement || null;

              return {
                ...payment,
                order: orderData,
                invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
                invoice_date: payment.created_at,
                amount: payment.montant,
                status: payment.status || 'completed',
                methode_paiement: finalPaymentMethod
              };
            }

            return {
              ...payment,
              order: null,
              methode_paiement: null,
              invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
              invoice_date: payment.created_at,
              amount: payment.montant,
              status: payment.status || 'completed'
            };
          } catch (err) {
            console.error(`❌ Error fetching order ${payment.commande_id}:`, err);
            return {
              ...payment,
              order: null,
              methode_paiement: null,
              invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
              invoice_date: payment.created_at,
              amount: payment.montant,
              status: payment.status || 'completed'
            };
          }
        })
      );

      setInvoices(invoicesWithOrders);
      setTotalInvoices(invoicesWithOrders.length);
    } catch (err) {
      console.error('Error loading invoices:', err);
      setError('Erreur lors du chargement des factures: ' + err.message);
      setInvoices([]);
      setTotalInvoices(0);
    } finally {
      setLoading(false);
    }
  };

  // Filter invoices based on search term and status
  const filteredInvoices = invoices.filter((invoice) => {
    const matchesSearch =
      invoice.invoice_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.nom_client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.email_commande?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.transaction_id?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !statusFilter || invoice.status?.toLowerCase() === statusFilter.toLowerCase();

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentInvoices = filteredInvoices.slice(startIndex, endIndex);

  // Reset to first page when search term or status filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Enhanced handlers
  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleStatusFilter = (event) => {
    setStatusFilter(event.target.value);
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleRefresh = () => {
    loadInvoices();
  };

  // Table columns configuration
  const columns = [
    { id: 'invoice_number', label: 'N° Facture', minWidth: 120 },
    { id: 'invoice_date', label: 'Date', minWidth: 150 },
    { id: 'client_name', label: 'Client', minWidth: 180 },
    { id: 'commande_id', label: 'Commande', minWidth: 100 },
    { id: 'amount', label: 'Montant', minWidth: 120 },
    { id: 'status', label: 'Statut', minWidth: 100 },
    { id: 'actions', label: 'Actions', minWidth: 120 }
  ];

  // Custom cell renderer
  const renderCell = (column, row, value) => {
    switch (column.id) {
      case 'invoice_number':
        return (
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.semibold,
                color: COLORS.primary.main
              }}
            >
              {row.invoice_number}
            </Typography>
            {row.transaction_id && (
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                ID: {row.transaction_id}
              </Typography>
            )}
          </Box>
        );
      case 'invoice_date':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
            {formatDate(row.invoice_date)}
          </Typography>
        );
      case 'client_name':
        return (
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.medium,
                display: 'flex',
                alignItems: 'center',
                gap: 0.5
              }}
            >
              <PersonIcon size={16} />
              {getClientName(row)}
            </Typography>
            {(row.order?.email_commande || row.order?.user?.email) && (
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                {row.order.email_commande || row.order.user?.email}
              </Typography>
            )}
          </Box>
        );
      case 'commande_id':
        return (
          <Chip
            label={`CMD-${row.commande_id}`}
            size="small"
            sx={{
              backgroundColor: COLORS.primary.light,
              color: COLORS.primary.main,
              fontFamily: TYPOGRAPHY.fontFamily.primary
            }}
          />
        );
      case 'amount':
        return (
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.success.main
            }}
          >
            {formatCurrency(row.amount)}
          </Typography>
        );
      case 'status':
        return (
          <Chip
            label={getStatusText(row.status)}
            size="small"
            sx={{
              backgroundColor: getStatusColorValue(row.status),
              color: 'white',
              fontFamily: TYPOGRAPHY.fontFamily.primary
            }}
          />
        );
      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Voir la facture">
              <IconButton
                size="small"
                onClick={() => handleViewInvoice(row)}
                sx={{
                  color: COLORS.primary.main,
                  '&:hover': {
                    backgroundColor: COLORS.primary.light,
                    transform: 'scale(1.1)'
                  }
                }}
              >
                <VisibilityIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Imprimer">
              <IconButton
                size="small"
                onClick={() => handlePrintInvoice(row)}
                sx={{
                  color: COLORS.secondary.main,
                  '&:hover': {
                    backgroundColor: COLORS.secondary.light,
                    transform: 'scale(1.1)'
                  }
                }}
              >
                <PrintIcon />
              </IconButton>
            </Tooltip>
          </Box>
        );
      default:
        return value || 'N/A';
    }
  };

  // Handle view invoice details
  const handleViewInvoice = async (invoice) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  // Handle print invoice
  const handlePrintInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
    setTimeout(() => {
      window.print();
    }, 300);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format currency - Using TND (Tunisian Dinar)
  const formatCurrency = (amount) => {
    if (!amount) return '0.000 DT';
    return `${parseFloat(amount).toFixed(3)} DT`;
  };

  // Get client name with improved fallback logic
  const getClientName = (invoice) => {
    const order = invoice.order;
    if (!order) return 'Client inconnu';

    if (order.user?.name) return order.user.name;
    if (order.client?.nom) return order.client.nom;
    if (order.nom_client) return order.nom_client;
    if (order.prenom_client && order.nom_client) {
      return `${order.prenom_client} ${order.nom_client}`;
    }
    if (order.email_commande) {
      return order.email_commande.split('@')[0];
    }

    return 'Client inconnu';
  };

  // Get status color (for Chip color prop)
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get status color value (for backgroundColor)
  const getStatusColorValue = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return COLORS.success.main;
      case 'pending':
        return COLORS.warning.main;
      case 'failed':
        return COLORS.error.main;
      default:
        return COLORS.grey[500];
    }
  };

  // Get status text
  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return 'Payée';
      case 'pending':
        return 'En attente';
      case 'failed':
        return 'Échec';
      default:
        return 'Inconnue';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Chargement des factures...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={loadInvoices}>
          Réessayer
        </Button>
      </Box>
    );
  }

  return (
    <MainCard title="Gestion des Factures - Enhanced">
      <Box sx={{ width: '100%' }}>
        {/* Filters and Search */}
        <StandardCard title="Recherche et Filtres" size="small" sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
            <TextField
              placeholder="Rechercher par numéro, client, email, transaction..."
              value={searchTerm}
              onChange={handleSearch}
              sx={{ flex: 1, minWidth: 250 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon size={20} color={COLORS.grey[500]} />
                  </InputAdornment>
                )
              }}
            />

            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>Statut</InputLabel>
              <Select value={statusFilter} onChange={handleStatusFilter} label="Statut">
                <MenuItem value="">Tous</MenuItem>
                <MenuItem value="completed">Payée</MenuItem>
                <MenuItem value="pending">En attente</MenuItem>
                <MenuItem value="failed">Échec</MenuItem>
              </Select>
            </FormControl>

            <Button variant="outlined" onClick={handleRefresh} disabled={loading} startIcon={<RefreshIcon />}>
              Actualiser
            </Button>
          </Box>
        </StandardCard>

        {/* Results Summary */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="h5"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontSize: TYPOGRAPHY.fontSize.lg,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.text.dark
            }}
          >
            Toutes les Factures ({totalInvoices})
          </Typography>
          {(searchTerm || statusFilter) && (
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                mt: 0.5
              }}
            >
              Résultats filtrés - Page {currentPage} sur {totalPages}
            </Typography>
          )}
        </Box>

        {/* Enhanced Table */}
        <StandardTable
          columns={columns}
          data={currentInvoices}
          loading={loading}
          error={error}
          emptyMessage={searchTerm ? 'Aucune facture ne correspond à votre recherche.' : 'Aucune facture disponible pour le moment.'}
          renderCell={renderCell}
          hover={true}
          pagination={{
            page: currentPage,
            totalPages: totalPages,
            onPageChange: handlePageChange
          }}
        />

        {/* Additional Info */}
        {!loading && !error && filteredInvoices.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                textAlign: 'center'
              }}
            >
              Affichage de {(currentPage - 1) * itemsPerPage + 1} à {Math.min(currentPage * itemsPerPage, filteredInvoices.length)} sur{' '}
              {filteredInvoices.length} facture(s)
            </Typography>
          </Box>
        )}
      </Box>

      {/* Simple Invoice Details Modal */}
      <Modal
        open={showInvoiceModal}
        onClose={() => setShowInvoiceModal(false)}
        sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        <Box
          sx={{
            width: '90%',
            maxWidth: 600,
            maxHeight: '90vh',
            bgcolor: 'background.paper',
            borderRadius: 2,
            boxShadow: 24,
            overflow: 'auto',
            p: 3
          }}
        >
          {selectedInvoice && (
            <>
              {/* Header */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ReceiptIcon />
                  Facture {selectedInvoice.invoice_number}
                </Typography>
                <IconButton onClick={() => setShowInvoiceModal(false)}>
                  <CloseIcon />
                </IconButton>
              </Box>

              {/* Invoice Details */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" color="primary" gutterBottom>
                    <PersonIcon sx={{ mr: 1 }} />
                    Informations Client
                  </Typography>
                  <Typography>
                    <strong>Client:</strong> {getClientName(selectedInvoice)}
                  </Typography>
                  <Typography>
                    <strong>Email:</strong> {selectedInvoice.order?.user?.email || selectedInvoice.order?.email_commande || 'Non spécifié'}
                  </Typography>
                  <Typography>
                    <strong>Commande:</strong> CMD-{selectedInvoice.commande_id}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" color="primary" gutterBottom>
                    <PaymentIcon sx={{ mr: 1 }} />
                    Informations Paiement
                  </Typography>
                  <Typography>
                    <strong>Date:</strong> {formatDate(selectedInvoice.invoice_date)}
                  </Typography>
                  <Typography>
                    <strong>Montant:</strong> {formatCurrency(selectedInvoice.amount)}
                  </Typography>
                  <Typography>
                    <strong>Méthode:</strong> {selectedInvoice.methode_paiement || 'Non spécifiée'}
                  </Typography>
                  <Typography>
                    <strong>Statut:</strong> {getStatusText(selectedInvoice.status)}
                  </Typography>
                </Grid>
              </Grid>

              {/* Actions */}
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button variant="outlined" onClick={() => setShowInvoiceModal(false)} startIcon={<CloseIcon />}>
                  Fermer
                </Button>
                <Button variant="contained" onClick={() => window.print()} startIcon={<PrintIcon />}>
                  Imprimer
                </Button>
              </Box>
            </>
          )}
        </Box>
      </Modal>
    </MainCard>
  );
};

export default InvoicesFixed;
