import React, { useState, useEffect } from 'react';
import { Box, Typo<PERSON>, TextField, InputAdornment, IconButton, Tooltip } from '@mui/material';
import {
  FaFileInvoice,
  FaEye,
  FaPrint,
  FaSearch,
  FaCalendarAlt,
  FaMoneyBillWave,
  FaUser,
  FaCreditCard,
  FaTimes,
  FaShoppingCart
} from 'react-icons/fa';
import { Modal, Button, Row, Col, Badge } from 'react-bootstrap';

// Standardized components
import StandardTable from '../../components/StandardTable';
import MainCard from '../../ui-component/cards/MainCard';

// Design system
import { COLORS, TYPOGRAPHY, BORDER_RADIUS } from '../../themes/designSystem';

const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

const InvoicesFixed = () => {
  // States
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Modal states
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);

  // Load invoices on component mount
  useEffect(() => {
    loadInvoices();
  }, [currentPage, searchTerm]);

  // Load invoices from API
  const loadInvoices = async () => {
    setLoading(true);
    setError('');
    try {
      console.log('🔄 Loading invoices...');
      const response = await fetch(`${API_URL}/commandes?page=${currentPage}&search=${searchTerm}&with=user,paiement`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📄 Invoices data:', data);

      if (data.success && data.data) {
        // Filter only paid orders (invoices)
        const paidOrders = data.data.filter(
          (order) => order.payment_status === 'completed' || order.payment_status === 'paid' || order.paiement
        );

        setInvoices(paidOrders);
        setTotalPages(data.last_page || 1);
        setTotalItems(data.total || paidOrders.length);
      } else {
        setInvoices([]);
      }
    } catch (error) {
      console.error('❌ Error loading invoices:', error);
      setError('Erreur lors du chargement des factures');
      setInvoices([]);
    } finally {
      setLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount) return '0,00 TND';
    const numAmount = parseFloat(amount);
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 2
    }).format(numAmount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Date invalide';
    }
  };

  // Get client name
  const getClientName = (invoice) => {
    if (invoice.user) {
      return `${invoice.user.prenom || ''} ${invoice.user.nom || ''}`.trim() || invoice.user.email;
    }
    return invoice.nom_commande || invoice.email_commande || 'Client anonyme';
  };

  // Get status text
  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
      case 'paid':
        return 'Payée';
      case 'pending':
        return 'En attente';
      case 'failed':
        return 'Échouée';
      default:
        return 'Payée';
    }
  };

  // Handle view invoice
  const handleViewInvoice = (invoice) => {
    console.log('👁️ View invoice:', invoice);
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  // Handle print invoice - Create professional print that matches modal content exactly
  const handlePrintInvoice = (invoice) => {
    console.log('🖨️ Print invoice:', invoice);

    // Wait for modal to be fully rendered if it's not open yet
    if (!showInvoiceModal) {
      setSelectedInvoice(invoice);
      setShowInvoiceModal(true);
      setTimeout(() => handlePrintInvoice(invoice), 500);
      return;
    }

    setTimeout(() => {
      const modalContent = document.getElementById('invoice-content');
      if (!modalContent) {
        alert("Contenu de facture non trouvé. Veuillez ouvrir la facture d'abord.");
        return;
      }

      // Create print window
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        alert("Impossible d'ouvrir la fenêtre d'impression. Veuillez autoriser les popups.");
        return;
      }

      // Create professional print HTML that matches the modal exactly
      const printHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Facture ${invoice.invoice_number || `FAC-${invoice.id}`} - JihenLine</title>
          <meta charset="utf-8">
          <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
          <style>
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              font-size: 12px;
              line-height: 1.4;
              color: #333;
              background: white;
              margin: 0;
              padding: 20px;
            }
            
            .invoice-details {
              max-width: 800px;
              margin: 0 auto;
              background: white;
            }
            
            .company-header {
              border-bottom: 3px solid #0d6efd;
              margin-bottom: 20px;
              padding-bottom: 15px;
            }
            
            .text-primary { color: #0d6efd !important; }
            .text-muted { color: #6c757d !important; }
            .text-success { color: #198754 !important; }
            .fw-bold { font-weight: bold !important; }
            .bg-light { 
              background-color: #f8f9fa !important; 
              border: 1px solid #dee2e6 !important;
              padding: 15px !important;
            }
            .border-primary { border-color: #0d6efd !important; }
            .border-3 { border-width: 3px !important; }
            .border-4 { border-width: 4px !important; }
            .border-start { border-left: 4px solid #0d6efd !important; }
            .bg-primary { background-color: #0d6efd !important; }
            .bg-opacity-10 { background-color: rgba(13, 110, 253, 0.1) !important; }
            
            .badge {
              display: inline-block;
              padding: 0.35em 0.65em;
              font-size: 0.75em;
              font-weight: 700;
              line-height: 1;
              color: #fff;
              text-align: center;
              white-space: nowrap;
              vertical-align: baseline;
              border-radius: 0.25rem;
            }
            
            .bg-success { background-color: #198754 !important; }
            .bg-secondary { background-color: #6c757d !important; }
            
            h6 { 
              font-size: 14px;
              margin-bottom: 10px;
              color: #0d6efd;
              border-bottom: 1px solid #dee2e6;
              padding-bottom: 5px;
            }
            
            .total-section {
              margin-top: 20px;
              padding: 15px;
              background-color: rgba(13, 110, 253, 0.1);
              border-left: 4px solid #0d6efd;
              border-radius: 0.25rem;
            }
            
            .row { display: flex; flex-wrap: wrap; }
            .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
            .col-md-6 { flex: 0 0 50%; max-width: 50%; }
            .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
            .text-end { text-align: right; }
            .mb-1 { margin-bottom: 0.25rem; }
            .mb-2 { margin-bottom: 0.5rem; }
            .mb-3 { margin-bottom: 1rem; }
            .mb-4 { margin-bottom: 1.5rem; }
            .pb-3 { padding-bottom: 1rem; }
            .p-3 { padding: 1rem; }
            .rounded { border-radius: 0.25rem; }
            .d-flex { display: flex; }
            .align-items-center { align-items: center; }
            .gap-3 > * { margin-right: 1rem; }
            .small { font-size: 0.875em; }
            .fs-6 { font-size: 1rem; }
            .px-3 { padding-left: 1rem; padding-right: 1rem; }
            .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
            .me-1 { margin-right: 0.25rem; }
            .me-2 { margin-right: 0.5rem; }
            
            @media print {
              body { 
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0;
                padding: 10px;
              }
              
              .invoice-details {
                max-width: none;
                margin: 0;
              }
              
              .no-print { display: none !important; }
            }
          </style>
        </head>
        <body>
          ${modalContent.innerHTML}
        </body>
        </html>
      `;

      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.focus();

      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    }, 100);
  };

  // Table columns
  const columns = [
    {
      key: 'invoice_number',
      label: 'N° Facture',
      render: (invoice) => invoice.invoice_number || `FAC-${invoice.id}`
    },
    {
      key: 'client',
      label: 'Client',
      render: (invoice) => getClientName(invoice)
    },
    {
      key: 'date',
      label: 'Date',
      render: (invoice) => formatDate(invoice.created_at)
    },
    {
      key: 'amount',
      label: 'Montant',
      render: (invoice) => formatCurrency(invoice.total_commande || invoice.amount)
    },
    {
      key: 'payment_method',
      label: 'Méthode',
      render: (invoice) => (
        <Badge bg="secondary" className="text-capitalize">
          {invoice.methode_paiement?.replace('_', ' ') || 'N/A'}
        </Badge>
      )
    },
    {
      key: 'status',
      label: 'Statut',
      render: (invoice) => <Badge bg="success">{getStatusText(invoice.payment_status)}</Badge>
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (invoice) => (
        <div className="d-flex gap-2">
          <Tooltip title="Voir la facture">
            <IconButton size="small" onClick={() => handleViewInvoice(invoice)} sx={{ color: COLORS.primary.main }}>
              <FaEye />
            </IconButton>
          </Tooltip>
          <Tooltip title="Imprimer la facture">
            <IconButton size="small" onClick={() => handlePrintInvoice(invoice)} sx={{ color: COLORS.success.main }}>
              <FaPrint />
            </IconButton>
          </Tooltip>
        </div>
      )
    }
  ];

  return (
    <MainCard
      title={
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FaFileInvoice size={24} color={COLORS.primary.main} />
          <Typography
            variant="h4"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.text.dark
            }}
          >
            Gestion des Factures
          </Typography>
        </Box>
      }
      secondary={
        <Typography
          variant="body2"
          sx={{
            fontFamily: TYPOGRAPHY.fontFamily.primary,
            color: COLORS.text.secondary
          }}
        >
          Consultez et imprimez toutes les factures payées
        </Typography>
      }
    >
      <Box sx={{ p: 3 }}>
        {/* Search Bar */}
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            placeholder="Rechercher par numéro de facture, client, email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: BORDER_RADIUS.md,
                fontSize: TYPOGRAPHY.fontSize.sm
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <FaSearch size={20} color={COLORS.grey[500]} />
                </InputAdornment>
              )
            }}
          />
        </Box>

        {/* Error Alert */}
        {error && (
          <Box sx={{ mb: 3 }}>
            <div className="alert alert-danger" role="alert">
              <strong>Erreur:</strong> {error}
            </div>
          </Box>
        )}

        {/* Invoices Table */}
        <StandardTable
          data={invoices}
          columns={columns}
          loading={loading}
          emptyMessage="Aucune facture trouvée"
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          onPageChange={setCurrentPage}
        />
      </Box>

      {/* Invoice Details Modal - Professional Bootstrap Modal */}
      <Modal show={showInvoiceModal} onHide={() => setShowInvoiceModal(false)} size="xl" centered backdrop="static">
        <Modal.Header closeButton className="bg-primary text-white">
          <Modal.Title className="d-flex align-items-center">
            <FaFileInvoice className="me-2" />
            Facture {selectedInvoice?.invoice_number || `FAC-${selectedInvoice?.id}`}
          </Modal.Title>
        </Modal.Header>

        <Modal.Body className="p-0" style={{ maxHeight: '70vh', overflowY: 'auto' }}>
          {selectedInvoice && (
            <div id="invoice-content" className="invoice-details p-4">
              {/* Company Header */}
              <div className="company-header mb-4 pb-3 border-bottom border-primary border-3">
                <Row>
                  <Col md={8}>
                    <h3 className="text-primary mb-1 fw-bold">JihenLine</h3>
                    <div className="text-muted small">
                      <div>Plateforme E-commerce</div>
                      <div>Email: <EMAIL></div>
                      <div>Téléphone: +216 XX XXX XXX</div>
                      <div>TVA: TN123456789</div>
                      <div>RC: B123456789</div>
                    </div>
                  </Col>
                  <Col md={4} className="text-end">
                    <h4 className="text-primary mb-1 fw-bold">FACTURE</h4>
                    <div className="fw-bold fs-5">{selectedInvoice.invoice_number || `FAC-${selectedInvoice.id}`}</div>
                    <div className="small text-muted">Date: {formatDate(selectedInvoice.invoice_date || selectedInvoice.created_at)}</div>
                    <div className="small text-muted">
                      Statut: <Badge bg="success">Payée</Badge>
                    </div>
                  </Col>
                </Row>
              </div>

              {/* Invoice Details */}
              <div className="invoice-header mb-3 p-3 bg-light rounded">
                <Row className="align-items-center">
                  <Col md={8}>
                    <h6 className="text-primary mb-2 d-flex align-items-center">
                      <FaFileInvoice className="me-2" />
                      Détails de la Facture
                    </h6>
                    <div className="d-flex flex-wrap gap-3 small text-muted">
                      <span>
                        <FaCalendarAlt className="me-1" />
                        Date: {formatDate(selectedInvoice.invoice_date || selectedInvoice.created_at)}
                      </span>
                      <span>
                        <FaShoppingCart className="me-1" />
                        Commande: CMD-{selectedInvoice.commande_id || selectedInvoice.id}
                      </span>
                      <span>
                        <FaMoneyBillWave className="me-1" />
                        Montant: {formatCurrency(selectedInvoice.amount || selectedInvoice.total_commande)}
                      </span>
                    </div>
                  </Col>
                  <Col md={4} className="text-end">
                    <Badge bg="success" className="fs-6 px-3 py-2">
                      Payée
                    </Badge>
                  </Col>
                </Row>
              </div>

              {/* Customer Information */}
              <div className="customer-info mb-3">
                <h6 className="text-primary mb-2 d-flex align-items-center">
                  <FaUser className="me-2" />
                  Informations Client
                </h6>
                <div className="bg-light p-3 rounded">
                  <Row>
                    <Col md={6}>
                      <div className="mb-2">
                        <strong className="text-muted small">Client:</strong>
                        <div>{getClientName(selectedInvoice)}</div>
                      </div>
                      <div className="mb-2">
                        <strong className="text-muted small">Email:</strong>
                        <div className="small">{selectedInvoice.user?.email || selectedInvoice.email_commande || 'Non spécifié'}</div>
                      </div>
                      {selectedInvoice.telephone_commande && (
                        <div className="mb-2">
                          <strong className="text-muted small">Téléphone:</strong>
                          <div className="small">{selectedInvoice.telephone_commande}</div>
                        </div>
                      )}
                    </Col>
                    <Col md={6}>
                      {selectedInvoice.adresse_commande && (
                        <div className="mb-2">
                          <strong className="text-muted small">Adresse:</strong>
                          <div className="small">
                            {selectedInvoice.adresse_commande}
                            {selectedInvoice.ville_commande && (
                              <>
                                <br />
                                {selectedInvoice.ville_commande}
                              </>
                            )}
                            {selectedInvoice.code_postal_commande && <> {selectedInvoice.code_postal_commande}</>}
                          </div>
                        </div>
                      )}
                    </Col>
                  </Row>
                </div>
              </div>

              {/* Payment Information */}
              <div className="payment-info mb-3">
                <h6 className="text-primary mb-2 d-flex align-items-center">
                  <FaCreditCard className="me-2" />
                  Informations de Paiement
                </h6>
                <div className="bg-light p-3 rounded">
                  <Row className="align-items-center">
                    <Col md={8}>
                      <div className="d-flex flex-wrap gap-3">
                        <div>
                          <strong className="text-muted small">Méthode:</strong>
                          <div className="small">
                            <Badge bg="secondary" className="text-capitalize">
                              {selectedInvoice.methode_paiement ? selectedInvoice.methode_paiement.replace('_', ' ') : 'Non spécifiée'}
                            </Badge>
                          </div>
                        </div>
                        {selectedInvoice.transaction_id && (
                          <div>
                            <strong className="text-muted small">Transaction ID:</strong>
                            <div className="small font-monospace">{selectedInvoice.transaction_id}</div>
                          </div>
                        )}
                        <div>
                          <strong className="text-muted small">Date de paiement:</strong>
                          <div className="small">{formatDate(selectedInvoice.created_at)}</div>
                        </div>
                      </div>
                    </Col>
                    <Col md={4} className="text-end">
                      <div className="text-success">
                        <div className="small text-muted">Montant Payé</div>
                        <h5 className="mb-0 text-success">
                          <FaMoneyBillWave className="me-1" />
                          {formatCurrency(selectedInvoice.amount || selectedInvoice.total_commande)}
                        </h5>
                      </div>
                    </Col>
                  </Row>
                </div>
              </div>

              {/* Total Section */}
              <div className="total-section mt-3 p-3 bg-primary bg-opacity-10 rounded border-start border-primary border-4">
                <Row className="align-items-center">
                  <Col md={8}>
                    <h6 className="text-primary mb-1 fw-bold">Montant Total TTC:</h6>
                    <small className="text-muted">TVA incluse (19%) - Facture générée le {formatDate(new Date().toISOString())}</small>
                  </Col>
                  <Col md={4} className="text-end">
                    <h4 className="text-primary fw-bold mb-0">
                      {formatCurrency(selectedInvoice.amount || selectedInvoice.total_commande)}
                    </h4>
                  </Col>
                </Row>
              </div>
            </div>
          )}
        </Modal.Body>

        <Modal.Footer className="bg-light border-0 py-2">
          <div className="d-flex justify-content-between w-100">
            <Button variant="outline-secondary" size="sm" onClick={() => setShowInvoiceModal(false)}>
              <FaTimes className="me-1" />
              Fermer
            </Button>
            <Button variant="primary" size="sm" onClick={() => selectedInvoice && handlePrintInvoice(selectedInvoice)}>
              <FaPrint className="me-1" />
              Imprimer
            </Button>
          </div>
        </Modal.Footer>
      </Modal>
    </MainCard>
  );
};

export default InvoicesFixed;
