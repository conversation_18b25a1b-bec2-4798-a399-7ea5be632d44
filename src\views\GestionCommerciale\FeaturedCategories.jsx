import React, { useEffect, useState } from 'react';
import { fetchCategories, fetchFeaturedCategories, setFeaturedCategory, reorderFeaturedCategories } from '../../services/categoryService';
import { Badge } from 'react-bootstrap';
import { Box, Typography, Alert } from '@mui/material';
import { FaStar, FaRegStar, FaArrowUp, FaArrowDown, FaEdit, FaTrashAlt } from 'react-icons/fa';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';
import MainCard from '../../ui-component/cards/MainCard';
import StandardButton from '../../components/StandardButton';
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';

export default function FeaturedCategories() {
  // Columns for featured categories table
  const featuredCategoriesColumns = [
    { id: 'order', label: 'Ordre', minWidth: 80 },
    { id: 'id', label: 'ID', minWidth: 60 },
    { id: 'nom', label: 'Nom', minWidth: 200 },
    { id: 'description', label: 'Description', minWidth: 250 },
    { id: 'actions', label: 'Actions', minWidth: 150, align: 'center' }
  ];

  // Columns for all categories table
  const allCategoriesColumns = [
    { id: 'id', label: 'ID', minWidth: 60 },
    { id: 'nom', label: 'Nom', minWidth: 200 },
    { id: 'description', label: 'Description', minWidth: 250 },
    { id: 'actions', label: 'Actions', minWidth: 120, align: 'center' }
  ];

  const [categories, setCategories] = useState([]);
  const [featuredCategories, setFeaturedCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Load data
  const loadData = async () => {
    console.log('🔄 Loading featured categories data...');
    setLoading(true);
    setError('');
    try {
      const [allCategories, featured] = await Promise.all([fetchCategories(), fetchFeaturedCategories()]);
      console.log('✅ All categories loaded:', allCategories);
      console.log('✅ Featured categories loaded:', featured);
      setCategories(allCategories);
      setFeaturedCategories(featured);
    } catch (e) {
      console.error('❌ Error loading featured categories data:', e);
      setError(e.message);
    }
    setLoading(false);
  };

  useEffect(() => {
    loadData();
  }, []);

  // Render cell content for featured categories
  const renderFeaturedCell = (column, row, value) => {
    // Find the index of this row in the featuredCategories array
    const index = featuredCategories.findIndex((cat) => cat.id === row.id);

    switch (column.id) {
      case 'order':
        return (
          <Badge bg="primary" pill>
            {index + 1}
          </Badge>
        );
      case 'id':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, fontWeight: TYPOGRAPHY.fontWeight.medium }}>
            {row.id}
          </Typography>
        );
      case 'nom':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, fontWeight: TYPOGRAPHY.fontWeight.semibold }}>
            {row.nom || row.nom_categorie}
          </Typography>
        );
      case 'description':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, color: COLORS.text.secondary }}>
            {row.description || row.description_categorie || 'Aucune description'}
          </Typography>
        );
      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton
              variant="outlined"
              size="small"
              onClick={() => moveCategory(index, 'up')}
              disabled={index === 0 || submitting}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaArrowUp />
            </StandardButton>
            <StandardButton
              variant="outlined"
              size="small"
              onClick={() => moveCategory(index, 'down')}
              disabled={index === featuredCategories.length - 1 || submitting}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaArrowDown />
            </StandardButton>
            <StandardButton
              variant="outlined"
              color="error"
              size="small"
              onClick={() => handleToggleFeatured(row, false)}
              disabled={submitting}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaTrashAlt />
            </StandardButton>
          </Box>
        );
      default:
        return value;
    }
  };

  // Render cell content for all categories
  const renderAllCategoriesCell = (column, row, value) => {
    switch (column.id) {
      case 'id':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, fontWeight: TYPOGRAPHY.fontWeight.medium }}>
            {row.id}
          </Typography>
        );
      case 'nom':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, fontWeight: TYPOGRAPHY.fontWeight.semibold }}>
            {row.nom || row.nom_categorie}
          </Typography>
        );
      case 'description':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, color: COLORS.text.secondary }}>
            {row.description || row.description_categorie || 'Aucune description'}
          </Typography>
        );
      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton
              variant="contained"
              size="small"
              onClick={() => handleToggleFeatured(row, true)}
              disabled={submitting}
              startIcon={<FaStar />}
            >
              Mettre en avant
            </StandardButton>
          </Box>
        );
      default:
        return value;
    }
  };

  // Handle featured status toggle
  const handleToggleFeatured = async (category, featured) => {
    try {
      setSubmitting(true);
      await setFeaturedCategory(category.id, {
        featured: featured,
        featured_order: featured
          ? featuredCategories.length > 0
            ? Math.max(...featuredCategories.map((c) => c.featured_order || 0)) + 1
            : 0
          : 0
      });
      setSuccess(`Catégorie ${featured ? 'ajoutée aux' : 'retirée des'} mises en avant`);
      loadData();
    } catch (e) {
      setError(e.message);
    } finally {
      setSubmitting(false);
      setTimeout(() => setSuccess(''), 3000);
    }
  };

  // Handle drag and drop reordering
  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const items = Array.from(featuredCategories);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update local state immediately for better UX
    setFeaturedCategories(items);

    // Prepare data for API
    const reorderData = {
      categories: items.map((item, index) => ({
        id: item.id,
        featured_order: index
      }))
    };

    try {
      setSubmitting(true);
      await reorderFeaturedCategories(reorderData);
      setSuccess('Ordre des catégories mis à jour');
    } catch (e) {
      setError(e.message);
      // Revert to previous state on error
      loadData();
    } finally {
      setSubmitting(false);
      setTimeout(() => setSuccess(''), 3000);
    }
  };

  // Move category up or down in order
  const moveCategory = async (index, direction) => {
    if ((direction === 'up' && index === 0) || (direction === 'down' && index === featuredCategories.length - 1)) {
      return;
    }

    const newIndex = direction === 'up' ? index - 1 : index + 1;
    const items = Array.from(featuredCategories);
    const [movedItem] = items.splice(index, 1);
    items.splice(newIndex, 0, movedItem);

    // Update local state immediately for better UX
    setFeaturedCategories(items);

    // Prepare data for API
    const reorderData = {
      categories: items.map((item, idx) => ({
        id: item.id,
        featured_order: idx
      }))
    };

    try {
      setSubmitting(true);
      await reorderFeaturedCategories(reorderData);
      setSuccess('Ordre des catégories mis à jour');
    } catch (e) {
      setError(e.message);
      // Revert to previous state on error
      loadData();
    } finally {
      setSubmitting(false);
      setTimeout(() => setSuccess(''), 3000);
    }
  };

  return (
    <MainCard title="Catégories Mises en Avant">
      <Box sx={{ width: '100%' }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="h4"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontSize: TYPOGRAPHY.fontSize.xl,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.text.dark,
              mb: 1
            }}
          >
            <FaStar style={{ marginRight: 8 }} />
            Catégories Mises en Avant
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary
            }}
          >
            Gérez les catégories qui apparaîtront en évidence sur la page d'accueil du site.
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        {/* Featured Categories Section */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h6"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.text.dark,
              mb: 2
            }}
          >
            Catégories mises en avant ({featuredCategories.length})
          </Typography>
          <StandardCard>
            <StandardTable
              columns={featuredCategoriesColumns}
              data={featuredCategories}
              loading={loading}
              renderCell={renderFeaturedCell}
              emptyMessage="Aucune catégorie mise en avant. Utilisez le tableau ci-dessous pour ajouter des catégories à mettre en avant."
              loadingMessage="Chargement des catégories..."
            />
          </StandardCard>
        </Box>

        {/* All Categories Section */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h6"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.text.dark,
              mb: 2
            }}
          >
            Toutes les catégories ({categories.filter((cat) => !featuredCategories.some((fc) => fc.id === cat.id)).length})
          </Typography>
          <StandardCard>
            <StandardTable
              columns={allCategoriesColumns}
              data={categories.filter((cat) => !featuredCategories.some((fc) => fc.id === cat.id))}
              loading={loading}
              renderCell={renderAllCategoriesCell}
              emptyMessage="Aucune catégorie trouvée."
              loadingMessage="Chargement des catégories..."
            />
          </StandardCard>
        </Box>
      </Box>
    </MainCard>
  );
}
