import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  LocalShipping as ShippingIcon,
  Payment as PaymentIcon,
  Person as PersonIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

// Standardized components
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';
import MainCard from '../../ui-component/cards/MainCard';
import ErrorDisplay from '../../components/ErrorDisplay';
import ErrorBoundary from '../../components/ErrorBoundary';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';

// Hooks
import { useOrderError } from '../../hooks/useApiError';

// Services
import { fetchOrdersFromLiveAPI } from '../../services/orderService';

const OrderListEnhanced = () => {
  const navigate = useNavigate();
  const { error, isRetrying, retryCount, handleOrderError, retry, clearError } = useOrderError();

  // State
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [paymentFilter, setPaymentFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);

  // Load orders from API
  const loadOrders = async (page = 1, search = '', status = '', payment = '') => {
    try {
      setLoading(true);
      clearError();

      const params = {
        page: page,
        per_page: 15
      };

      if (search) params.search = search;
      if (status) params.status = status;
      if (payment) params.payment_method = payment;

      console.log('🔄 Loading orders with params:', params);

      const result = await fetchOrdersFromLiveAPI(params);
      console.log('✅ Orders loaded successfully:', result);

      setOrders(result.data || []);
      setCurrentPage(result.pagination.current_page || 1);
      setTotalPages(result.pagination.last_page || 1);
      setTotalOrders(result.pagination.total || 0);
    } catch (error) {
      console.error('❌ Error loading orders:', error);
      handleOrderError(error);
    } finally {
      setLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    loadOrders(currentPage, searchTerm, statusFilter, paymentFilter);
  }, [currentPage]);

  // Handlers
  const handleSearch = (event) => {
    const value = event.target.value;
    setSearchTerm(value);
    setCurrentPage(1);

    // Debounce search
    const timeoutId = setTimeout(() => {
      loadOrders(1, value, statusFilter, paymentFilter);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  const handleStatusFilter = (event) => {
    const value = event.target.value;
    setStatusFilter(value);
    setCurrentPage(1);
    loadOrders(1, searchTerm, value, paymentFilter);
  };

  const handlePaymentFilter = (event) => {
    const value = event.target.value;
    setPaymentFilter(value);
    setCurrentPage(1);
    loadOrders(1, searchTerm, statusFilter, value);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleViewOrder = (order) => {
    navigate(`/app/orders/${order.id}`);
  };

  const handleRefresh = () => {
    loadOrders(currentPage, searchTerm, statusFilter, paymentFilter);
  };

  // Utility functions
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    if (!amount) return '0.00 DT';
    return `${parseFloat(amount).toFixed(2)} DT`;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmee':
      case 'confirmed':
        return COLORS.success.main;
      case 'en_attente':
      case 'pending':
        return COLORS.warning.main;
      case 'annulee':
      case 'cancelled':
        return COLORS.error.main;
      case 'livree':
      case 'delivered':
        return COLORS.primary.main;
      default:
        return COLORS.grey[500];
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'paid':
      case 'completed':
        return COLORS.success.main;
      case 'pending':
        return COLORS.warning.main;
      case 'failed':
      case 'cancelled':
        return COLORS.error.main;
      default:
        return COLORS.grey[500];
    }
  };

  const getPaymentMethodLabel = (method) => {
    switch (method) {
      case 'stripe':
        return 'Carte bancaire';
      case 'cash_on_delivery':
        return 'Paiement à la livraison';
      case 'local_pickup':
        return 'Retrait en magasin';
      default:
        return method || 'Non spécifié';
    }
  };

  // Table columns configuration
  const columns = [
    { id: 'numero_commande', label: 'N° Commande', minWidth: 150 },
    { id: 'user', label: 'Client', minWidth: 180 },
    { id: 'created_at', label: 'Date', minWidth: 140 },
    { id: 'total_commande', label: 'Total', minWidth: 100 },
    { id: 'methode_paiement', label: 'Paiement', minWidth: 140 },
    { id: 'status', label: 'Statut', minWidth: 120 },
    { id: 'payment_status', label: 'Paiement', minWidth: 100 },
    { id: 'actions', label: 'Actions', minWidth: 120 }
  ];

  // Custom cell renderer
  const renderCell = (column, row, value) => {
    switch (column.id) {
      case 'numero_commande':
        return (
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.semibold,
                color: COLORS.primary.main
              }}
            >
              {row.numero_commande || `CMD-${row.id}`}
            </Typography>
            <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
              ID: {row.id}
            </Typography>
          </Box>
        );

      case 'user':
        return (
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.medium,
                display: 'flex',
                alignItems: 'center',
                gap: 0.5
              }}
            >
              <PersonIcon size={16} />
              {row.user?.name || 'Client'}
            </Typography>
            <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
              {row.user?.email || row.email_commande || 'N/A'}
            </Typography>
          </Box>
        );

      case 'created_at':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
            {formatDate(row.created_at)}
          </Typography>
        );

      case 'total_commande':
        return (
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.success.main
            }}
          >
            {formatCurrency(row.total_commande)}
          </Typography>
        );

      case 'methode_paiement':
        return (
          <Chip
            icon={<PaymentIcon />}
            label={getPaymentMethodLabel(row.methode_paiement)}
            size="small"
            sx={{
              backgroundColor: COLORS.grey[200],
              color: COLORS.text.dark,
              fontFamily: TYPOGRAPHY.fontFamily.primary
            }}
          />
        );

      case 'status':
        return (
          <Chip
            label={row.status_label || row.status}
            size="small"
            sx={{
              backgroundColor: getStatusColor(row.status),
              color: 'white',
              fontFamily: TYPOGRAPHY.fontFamily.primary
            }}
          />
        );

      case 'payment_status':
        return (
          <Chip
            label={row.payment_status === 'paid' ? 'Payé' : row.payment_status === 'pending' ? 'En attente' : row.payment_status}
            size="small"
            sx={{
              backgroundColor: getPaymentStatusColor(row.payment_status),
              color: 'white',
              fontFamily: TYPOGRAPHY.fontFamily.primary
            }}
          />
        );

      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Voir les détails">
              <IconButton
                size="small"
                onClick={() => handleViewOrder(row)}
                sx={{
                  color: COLORS.primary.main,
                  '&:hover': {
                    backgroundColor: COLORS.primary.light,
                    transform: 'scale(1.1)'
                  }
                }}
              >
                <VisibilityIcon />
              </IconButton>
            </Tooltip>
          </Box>
        );

      default:
        return value || 'N/A';
    }
  };

  return (
    <ErrorBoundary>
      <MainCard title="Gestion des Commandes - Enhanced">
        <Box sx={{ width: '100%' }}>
          {/* Filters and Search */}
          <StandardCard title="Recherche et Filtres" size="small" sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
              <TextField
                placeholder="Rechercher par numéro, client, email..."
                value={searchTerm}
                onChange={handleSearch}
                sx={{ flex: 1, minWidth: 250 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon size={20} color={COLORS.grey[500]} />
                    </InputAdornment>
                  )
                }}
              />

              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>Statut</InputLabel>
                <Select value={statusFilter} onChange={handleStatusFilter} label="Statut">
                  <MenuItem value="">Tous</MenuItem>
                  <MenuItem value="en_attente">En attente</MenuItem>
                  <MenuItem value="confirmee">Confirmée</MenuItem>
                  <MenuItem value="livree">Livrée</MenuItem>
                  <MenuItem value="annulee">Annulée</MenuItem>
                </Select>
              </FormControl>

              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>Paiement</InputLabel>
                <Select value={paymentFilter} onChange={handlePaymentFilter} label="Paiement">
                  <MenuItem value="">Tous</MenuItem>
                  <MenuItem value="stripe">Carte bancaire</MenuItem>
                  <MenuItem value="cash_on_delivery">À la livraison</MenuItem>
                  <MenuItem value="local_pickup">Retrait magasin</MenuItem>
                </Select>
              </FormControl>

              <Button variant="outlined" onClick={handleRefresh} disabled={loading} startIcon={<RefreshIcon />}>
                Actualiser
              </Button>
            </Box>
          </StandardCard>

          {/* Results Summary */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="h5"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontSize: TYPOGRAPHY.fontSize.lg,
                fontWeight: TYPOGRAPHY.fontWeight.semibold,
                color: COLORS.text.dark
              }}
            >
              Toutes les Commandes ({totalOrders})
            </Typography>
            {(searchTerm || statusFilter || paymentFilter) && (
              <Typography
                variant="body2"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  color: COLORS.text.secondary,
                  mt: 0.5
                }}
              >
                Résultats filtrés - Page {currentPage} sur {totalPages}
              </Typography>
            )}
          </Box>

          {/* Enhanced Error Display */}
          {error && (
            <ErrorDisplay
              error={error}
              onRetry={() => retry(() => loadOrders(currentPage, searchTerm, statusFilter, paymentFilter))}
              isRetrying={isRetrying}
              retryCount={retryCount}
              compact={true}
            />
          )}

          {/* Enhanced Table */}
          <StandardTable
            columns={columns}
            data={orders}
            loading={loading}
            error={error}
            emptyMessage="Aucune commande trouvée"
            renderCell={renderCell}
            hover={true}
            pagination={{
              page: currentPage,
              totalPages: totalPages,
              onPageChange: handlePageChange
            }}
          />

          {/* Additional Info */}
          {!loading && !error && orders.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  color: COLORS.text.secondary,
                  textAlign: 'center'
                }}
              >
                Affichage de {(currentPage - 1) * 15 + 1} à {Math.min(currentPage * 15, totalOrders)} sur {totalOrders} commande(s)
              </Typography>
            </Box>
          )}
        </Box>
      </MainCard>
    </ErrorBoundary>
  );
};

export default OrderListEnhanced;
