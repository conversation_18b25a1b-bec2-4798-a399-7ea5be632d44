// assets
import { IconClipboardList, IconList, IconTags, IconFile, IconRocket, IconBug } from '@tabler/icons-react';

// constant
const icons = {
  IconClipboardList,
  IconList,
  IconTags,
  IconFile,
  IconRocket,
  IconBug
};

// ==============================|| ORDER MANAGEMENT MENU ITEMS ||============================== //

const orderManagement = {
  id: 'order-management',
  title: 'Gestion des Commandes',
  type: 'group',
  children: [
    {
      id: 'orders',
      title: 'Commandes',
      type: 'collapse',
      icon: icons.IconClipboardList,
      children: [
        {
          id: 'order-list-simple',
          title: 'Liste des Commandes (Simple)',
          type: 'item',
          url: '/app/orders',
          breadcrumbs: false
        },
        {
          id: 'order-list-enhanced',
          title: 'Liste des Commandes (Enhanced)',
          type: 'item',
          url: '/app/orders-enhanced',
          breadcrumbs: false
        },
        {
          id: 'order-list-test',
          title: '🧪 Test Actions',
          type: 'item',
          url: '/app/orders-test',
          breadcrumbs: false
        },
        {
          id: 'order-demo',
          title: '🚀 Démo API Live',
          type: 'item',
          url: '/app/orders-demo',
          icon: icons.IconRocket,
          breadcrumbs: false
        },
        {
          id: 'order-statuses',
          title: 'Statuts des Commandes',
          type: 'item',
          url: '/app/order-statuses',
          breadcrumbs: false
        }
      ]
    },
    {
      id: 'invoices',
      title: 'Factures',
      type: 'item',
      url: '/app/invoices',
      icon: icons.IconFile,
      breadcrumbs: false
    }
  ]
};

export default orderManagement;
