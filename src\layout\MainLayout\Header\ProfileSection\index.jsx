import { useRef, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import StandardButton from '../../../../components/StandardButton';
import Transitions from 'ui-component/extended/Transitions';
import useConfig from 'hooks/useConfig';
import { useAuth } from 'contexts/AuthContext';

// Design system
import { COLORS, TYPOGRAPHY } from 'themes/designSystem';

// assets
import User1 from 'assets/images/users/user-round.svg';
import { IconLogout, IconSettings, IconUser } from '@tabler/icons-react';
import { CircularProgress } from '@mui/material';

// ==============================|| PROFILE MENU ||============================== //

export default function ProfileSection() {
  const theme = useTheme();
  const navigate = useNavigate();
  const { borderRadius } = useConfig();
  const { user, logout, refreshUser } = useAuth();

  const [selectedIndex] = useState(-1);
  const [open, setOpen] = useState(false);
  const [loggingOut, setLoggingOut] = useState(false);

  /**
   * anchorRef is used on different components and specifying one type leads to other components throwing an error
   * */
  const anchorRef = useRef(null);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }

    setOpen(false);
  };

  const handleLogout = async () => {
    try {
      setLoggingOut(true);
      setOpen(false); // Close the menu
      await logout();
    } catch (error) {
      navigate('/pages/login');
    } finally {
      setLoggingOut(false);
    }
  };

  const handleProfileClick = () => {
    setOpen(false);
    navigate('/app/profile');
  };

  const prevOpen = useRef(open);
  useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef.current.focus();
    }

    prevOpen.current = open;
  }, [open]);

  return (
    <>
      <Chip
        sx={{
          ml: 2,
          height: '48px',
          alignItems: 'center',
          borderRadius: '27px',
          '& .MuiChip-label': {
            lineHeight: 0
          }
        }}
        icon={
          <Avatar
            src={User1}
            alt="user-images"
            sx={{
              ...theme.typography.mediumAvatar,
              margin: '8px 0 8px 8px !important',
              cursor: 'pointer'
            }}
            ref={anchorRef}
            aria-controls={open ? 'menu-list-grow' : undefined}
            aria-haspopup="true"
            color="inherit"
          />
        }
        label={<IconSettings stroke={1.5} size="24px" />}
        ref={anchorRef}
        aria-controls={open ? 'menu-list-grow' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        color="primary"
        aria-label="user-account"
      />
      <Popper
        placement="bottom"
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 14]
            }
          }
        ]}
      >
        {({ TransitionProps }) => (
          <ClickAwayListener onClickAway={handleClose}>
            <Transitions in={open} {...TransitionProps}>
              <Paper>
                {open && (
                  <MainCard border={false} elevation={16} content={false} boxShadow shadow={theme.shadows[16]}>
                    <Box sx={{ p: 2, pb: 0 }}>
                      <Stack spacing={1}>
                        {/* User Name */}
                        <Stack direction="row" spacing={0.5} sx={{ alignItems: 'center' }}>
                          <Typography
                            variant="h4"
                            sx={{
                              fontFamily: TYPOGRAPHY.fontFamily.primary,
                              fontWeight: TYPOGRAPHY.fontWeight.semibold,
                              color: COLORS.text.dark
                            }}
                          >
                            Bienvenue,
                          </Typography>
                          <Typography
                            component="span"
                            variant="h4"
                            sx={{
                              fontWeight: 400,
                              fontFamily: TYPOGRAPHY.fontFamily.primary,
                              color: COLORS.text.dark
                            }}
                          >
                            {user?.name || user?.preferred_username || 'Utilisateur'}
                          </Typography>
                        </Stack>

                        {/* User Email */}
                        {user?.email && (
                          <Typography
                            variant="body2"
                            color="textSecondary"
                            sx={{
                              fontFamily: TYPOGRAPHY.fontFamily.primary,
                              color: COLORS.text.secondary
                            }}
                          >
                            {user.email}
                          </Typography>
                        )}
                      </Stack>
                      <Divider sx={{ mt: 2 }} />
                    </Box>
                    <Box sx={{ p: 2, py: 1 }}>
                      <List
                        component="nav"
                        sx={{
                          width: '100%',
                          maxWidth: 350,
                          minWidth: 300,
                          borderRadius: `${borderRadius}px`,
                          '& .MuiListItemButton-root': { mt: 0.5 }
                        }}
                      >
                        <ListItemButton
                          sx={{
                            borderRadius: `${borderRadius}px`,
                            '&:hover': {
                              bgcolor: COLORS.primary.light + '20'
                            }
                          }}
                          selected={selectedIndex === 0}
                          onClick={handleProfileClick}
                        >
                          <ListItemIcon>
                            <IconUser stroke={1.5} size="20px" />
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Typography
                                variant="body2"
                                sx={{
                                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                                  fontWeight: TYPOGRAPHY.fontWeight.medium,
                                  color: COLORS.text.dark
                                }}
                              >
                                Mon Profil
                              </Typography>
                            }
                            secondary={
                              <Typography
                                variant="caption"
                                color="textSecondary"
                                sx={{
                                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                                  color: COLORS.text.secondary
                                }}
                              >
                                Voir et modifier le profil
                              </Typography>
                            }
                          />
                        </ListItemButton>

                        <Divider sx={{ my: 1 }} />

                        <ListItemButton
                          sx={{
                            borderRadius: `${borderRadius}px`,
                            '&:hover': {
                              bgcolor: COLORS.error.light + '20'
                            }
                          }}
                          selected={selectedIndex === 4}
                          onClick={handleLogout}
                          disabled={loggingOut}
                        >
                          <ListItemIcon>
                            {loggingOut ? <CircularProgress size={20} /> : <IconLogout stroke={1.5} size="20px" />}
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Typography
                                variant="body2"
                                sx={{
                                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                                  fontWeight: TYPOGRAPHY.fontWeight.medium,
                                  color: loggingOut ? COLORS.text.secondary : COLORS.error.main
                                }}
                              >
                                {loggingOut ? 'Déconnexion...' : 'Déconnexion'}
                              </Typography>
                            }
                          />
                        </ListItemButton>
                      </List>
                    </Box>
                  </MainCard>
                )}
              </Paper>
            </Transitions>
          </ClickAwayListener>
        )}
      </Popper>
    </>
  );
}
