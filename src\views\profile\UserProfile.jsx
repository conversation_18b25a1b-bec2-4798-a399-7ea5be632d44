import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Grid,
  Divider,
  Button,
  Alert,
  CircularProgress,
  Stack,
  Paper
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { IconRefresh, IconUser, IconMail, IconKey, IconShield, IconLogout } from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';

// project imports
import MainCard from '../../ui-component/cards/MainCard';
import StandardButton from '../../components/StandardButton';
import StandardCard from '../../components/StandardCard';
import { useAuth } from '../../contexts/AuthContext';
import authService from '../../services/authService';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';

// assets
import User1 from '../../assets/images/users/user-round.svg';

// ==============================|| USER PROFILE PAGE ||============================== //

export default function UserProfile() {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, refreshUser, logout } = useAuth();
  const [loading, setLoading] = useState(false);
  const [loggingOut, setLoggingOut] = useState(false);
  const [error, setError] = useState('');
  const [lastUpdated, setLastUpdated] = useState(null);

  // Refresh user data from API
  const handleRefreshUserData = async () => {
    try {
      setLoading(true);
      setError('');
      await refreshUser();
      setLastUpdated(new Date());
    } catch (error) {
      setError("Échec de l'actualisation des données utilisateur. Veuillez réessayer.");
    } finally {
      setLoading(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      setLoggingOut(true);
      await logout();
    } catch (error) {
      navigate('/pages/login');
    } finally {
      setLoggingOut(false);
    }
  };

  // Auto-refresh user data on component mount
  useEffect(() => {
    handleRefreshUserData();
  }, []);

  if (!user) {
    return (
      <MainCard>
        <Box sx={{ width: '100%' }}>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        </Box>
      </MainCard>
    );
  }

  return (
    <MainCard>
      <Box sx={{ width: '100%' }}>
        {/* Breadcrumb - Design System Style */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.sm
            }}
          >
            Accueil &gt; Mon Profil
          </Typography>
        </Box>

        {/* Header - Design System Style */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography
              variant="h3"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.bold,
                color: COLORS.text.dark,
                mb: 1
              }}
            >
              Mon Profil
            </Typography>
            <Typography
              variant="body1"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                fontSize: TYPOGRAPHY.fontSize.md
              }}
            >
              Gérez vos informations personnelles et paramètres de compte
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <StandardButton
              variant="outline"
              size="small"
              startIcon={loading ? <CircularProgress size={16} /> : <IconRefresh />}
              onClick={handleRefreshUserData}
              disabled={loading || loggingOut}
            >
              {loading ? 'Actualisation...' : 'Actualiser'}
            </StandardButton>
            <StandardButton
              variant="error"
              size="small"
              startIcon={loggingOut ? <CircularProgress size={16} /> : <IconLogout />}
              onClick={handleLogout}
              disabled={loading || loggingOut}
            >
              {loggingOut ? 'Déconnexion...' : 'Déconnexion'}
            </StandardButton>
          </Box>
        </Box>
        <Grid container spacing={3}>
          {/* Error Display */}
          {error && (
            <Grid item xs={12}>
              <Alert severity="error" onClose={() => setError('')}>
                {error}
              </Alert>
            </Grid>
          )}

          {/* Profile Header */}
          <Grid item xs={12}>
            <StandardCard sx={{ p: 3 }}>
              <Stack direction="row" spacing={3} alignItems="center">
                <Avatar
                  src={User1}
                  alt="Avatar utilisateur"
                  sx={{
                    width: 80,
                    height: 80,
                    border: `3px solid ${theme.palette.primary.main}`
                  }}
                />
                <Box flex={1}>
                  <Typography
                    variant="h3"
                    gutterBottom
                    sx={{
                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                      fontWeight: TYPOGRAPHY.fontWeight.bold,
                      color: COLORS.text.dark
                    }}
                  >
                    {user.name || user.preferred_username || 'Utilisateur Inconnu'}
                  </Typography>
                  <Typography
                    variant="body1"
                    color="textSecondary"
                    gutterBottom
                    sx={{
                      fontFamily: TYPOGRAPHY.fontFamily.primary,
                      color: COLORS.text.secondary
                    }}
                  >
                    {user.email || 'Aucun email fourni'}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                    {user.roles?.map((role, index) => (
                      <Chip
                        key={index}
                        label={role}
                        size="small"
                        color={role === 'admin' ? 'error' : role === 'partenaire' ? 'warning' : 'primary'}
                        icon={<IconShield size={16} />}
                      />
                    )) || <Chip label="Aucun rôle assigné" size="small" color="default" />}
                  </Box>
                </Box>
              </Stack>
            </StandardCard>
          </Grid>

          {/* User Details */}
          <Grid item xs={12} md={6}>
            <StandardCard>
              <CardContent>
                <Typography
                  variant="h5"
                  gutterBottom
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontWeight: TYPOGRAPHY.fontWeight.semibold,
                    color: COLORS.text.dark
                  }}
                >
                  <IconUser size={20} />
                  Informations Personnelles
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Stack spacing={2}>
                  <Box>
                    <Typography
                      variant="subtitle2"
                      color="textSecondary"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.medium,
                        color: COLORS.text.secondary
                      }}
                    >
                      Nom Complet
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        color: COLORS.text.dark
                      }}
                    >
                      {user.name || user.preferred_username || 'Non fourni'}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography
                      variant="subtitle2"
                      color="textSecondary"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.medium,
                        color: COLORS.text.secondary
                      }}
                    >
                      Adresse Email
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        color: COLORS.text.dark
                      }}
                    >
                      <IconMail size={16} />
                      {user.email || 'Non fourni'}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography
                      variant="subtitle2"
                      color="textSecondary"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.medium,
                        color: COLORS.text.secondary
                      }}
                    >
                      ID Utilisateur
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        fontFamily: 'monospace',
                        fontSize: '0.875rem',
                        color: COLORS.text.dark
                      }}
                    >
                      {user.id || 'Non disponible'}
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </StandardCard>
          </Grid>

          {/* Access & Permissions */}
          <Grid item xs={12} md={6}>
            <StandardCard>
              <CardContent>
                <Typography
                  variant="h5"
                  gutterBottom
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontWeight: TYPOGRAPHY.fontWeight.semibold,
                    color: COLORS.text.dark
                  }}
                >
                  <IconKey size={20} />
                  Accès et Permissions
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Stack spacing={2}>
                  <Box>
                    <Typography
                      variant="subtitle2"
                      color="textSecondary"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.medium,
                        color: COLORS.text.secondary
                      }}
                    >
                      Type de Compte
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        color: COLORS.text.dark
                      }}
                    >
                      Compte Utilisateur
                    </Typography>
                  </Box>

                  <Box>
                    <Typography
                      variant="subtitle2"
                      color="textSecondary"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.medium,
                        color: COLORS.text.secondary
                      }}
                    >
                      Rôles Assignés
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                      {user.roles?.map((role, index) => (
                        <Chip
                          key={index}
                          label={role}
                          size="small"
                          variant="filled"
                          color={role === 'admin' ? 'error' : role === 'partenaire' ? 'warning' : 'primary'}
                        />
                      )) || (
                        <Typography
                          variant="body2"
                          color="textSecondary"
                          sx={{
                            fontFamily: TYPOGRAPHY.fontFamily.primary,
                            color: COLORS.text.secondary
                          }}
                        >
                          Aucun rôle assigné
                        </Typography>
                      )}
                    </Box>
                  </Box>

                  <Box>
                    <Typography
                      variant="subtitle2"
                      color="textSecondary"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.medium,
                        color: COLORS.text.secondary
                      }}
                    >
                      Méthode d'Authentification
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        color: COLORS.text.dark
                      }}
                    >
                      Keycloak SSO
                    </Typography>
                  </Box>

                  <Box>
                    <Typography
                      variant="subtitle2"
                      color="textSecondary"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.medium,
                        color: COLORS.text.secondary
                      }}
                    >
                      Statut du Compte
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        fontWeight: 'bold',
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        color: COLORS.success.main
                      }}
                    >
                      Compte Actif
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </StandardCard>
          </Grid>
        </Grid>
      </Box>
    </MainCard>
  );
}
