// API Diagnostic Tool
// Outil de diagnostic pour identifier les problèmes de connexion API

const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

export const runApiDiagnostic = async () => {
  const results = {
    timestamp: new Date().toISOString(),
    environment: {
      apiUrl: API_URL,
      hasToken: !!localStorage.getItem('access_token'),
      tokenLength: localStorage.getItem('access_token')?.length || 0,
      userAgent: navigator.userAgent,
      online: navigator.onLine
    },
    tests: []
  };

  console.log('🔍 Starting API Diagnostic...');
  console.log('📊 Environment:', results.environment);

  // Test 1: Basic connectivity
  try {
    console.log('🧪 Test 1: Basic connectivity');
    const response = await fetch(API_URL.replace('/api', ''), {
      method: 'GET',
      mode: 'cors'
    });
    
    results.tests.push({
      name: 'Basic Connectivity',
      status: response.ok ? 'PASS' : 'FAIL',
      details: {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      }
    });
  } catch (error) {
    results.tests.push({
      name: 'Basic Connectivity',
      status: 'FAIL',
      error: error.message,
      details: { type: error.name, code: error.code }
    });
  }

  // Test 2: API endpoint availability
  try {
    console.log('🧪 Test 2: API endpoint availability');
    const response = await fetch(`${API_URL}/commandes`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    const responseText = await response.text();
    let responseData;
    
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }

    results.tests.push({
      name: 'API Endpoint (/commandes)',
      status: response.ok ? 'PASS' : 'FAIL',
      details: {
        status: response.status,
        statusText: response.statusText,
        responseType: typeof responseData,
        responsePreview: typeof responseData === 'string' 
          ? responseText.substring(0, 200) 
          : JSON.stringify(responseData).substring(0, 200)
      }
    });
  } catch (error) {
    results.tests.push({
      name: 'API Endpoint (/commandes)',
      status: 'FAIL',
      error: error.message,
      details: { type: error.name, code: error.code }
    });
  }

  // Test 3: Authenticated request
  const token = localStorage.getItem('access_token');
  if (token) {
    try {
      console.log('🧪 Test 3: Authenticated request');
      const response = await fetch(`${API_URL}/commandes`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }

      results.tests.push({
        name: 'Authenticated Request',
        status: response.ok ? 'PASS' : 'FAIL',
        details: {
          status: response.status,
          statusText: response.statusText,
          hasAuthHeader: response.headers.get('authorization') !== null,
          responseType: typeof responseData,
          responsePreview: typeof responseData === 'string' 
            ? responseText.substring(0, 200) 
            : JSON.stringify(responseData).substring(0, 200)
        }
      });
    } catch (error) {
      results.tests.push({
        name: 'Authenticated Request',
        status: 'FAIL',
        error: error.message,
        details: { type: error.name, code: error.code }
      });
    }
  } else {
    results.tests.push({
      name: 'Authenticated Request',
      status: 'SKIP',
      details: { reason: 'No authentication token found' }
    });
  }

  // Test 4: CORS check
  try {
    console.log('🧪 Test 4: CORS check');
    const response = await fetch(`${API_URL}/commandes`, {
      method: 'OPTIONS'
    });

    results.tests.push({
      name: 'CORS Check',
      status: response.ok ? 'PASS' : 'FAIL',
      details: {
        status: response.status,
        allowOrigin: response.headers.get('Access-Control-Allow-Origin'),
        allowMethods: response.headers.get('Access-Control-Allow-Methods'),
        allowHeaders: response.headers.get('Access-Control-Allow-Headers')
      }
    });
  } catch (error) {
    results.tests.push({
      name: 'CORS Check',
      status: 'FAIL',
      error: error.message,
      details: { type: error.name, code: error.code }
    });
  }

  console.log('✅ API Diagnostic completed');
  console.log('📋 Results:', results);

  return results;
};

export const generateDiagnosticReport = (results) => {
  const passedTests = results.tests.filter(t => t.status === 'PASS').length;
  const failedTests = results.tests.filter(t => t.status === 'FAIL').length;
  const skippedTests = results.tests.filter(t => t.status === 'SKIP').length;

  let report = `
🔍 DIAGNOSTIC API - ${new Date(results.timestamp).toLocaleString()}

📊 ENVIRONNEMENT:
- URL API: ${results.environment.apiUrl}
- Token d'authentification: ${results.environment.hasToken ? '✅ Présent' : '❌ Absent'}
- Connexion internet: ${results.environment.online ? '✅ En ligne' : '❌ Hors ligne'}

📋 RÉSULTATS DES TESTS:
- ✅ Réussis: ${passedTests}
- ❌ Échoués: ${failedTests}
- ⏭️ Ignorés: ${skippedTests}

📝 DÉTAILS:
`;

  results.tests.forEach(test => {
    const icon = test.status === 'PASS' ? '✅' : test.status === 'FAIL' ? '❌' : '⏭️';
    report += `\n${icon} ${test.name}: ${test.status}`;
    
    if (test.error) {
      report += `\n   Erreur: ${test.error}`;
    }
    
    if (test.details) {
      if (test.details.status) {
        report += `\n   Status HTTP: ${test.details.status}`;
      }
      if (test.details.responsePreview) {
        report += `\n   Aperçu réponse: ${test.details.responsePreview}`;
      }
    }
  });

  return report;
};

export const getRecommendations = (results) => {
  const recommendations = [];
  
  const failedTests = results.tests.filter(t => t.status === 'FAIL');
  
  if (!results.environment.online) {
    recommendations.push('🌐 Vérifiez votre connexion internet');
  }
  
  if (!results.environment.hasToken) {
    recommendations.push('🔑 Connectez-vous pour obtenir un token d\'authentification');
  }
  
  const connectivityTest = results.tests.find(t => t.name === 'Basic Connectivity');
  if (connectivityTest?.status === 'FAIL') {
    recommendations.push('🔌 Le serveur API semble inaccessible. Vérifiez l\'URL ou contactez l\'administrateur');
  }
  
  const authTest = results.tests.find(t => t.name === 'Authenticated Request');
  if (authTest?.status === 'FAIL' && authTest.details?.status === 401) {
    recommendations.push('🔐 Token d\'authentification expiré. Reconnectez-vous');
  }
  
  if (authTest?.status === 'FAIL' && authTest.details?.status === 500) {
    recommendations.push('⚠️ Erreur serveur détectée. Le problème vient du côté serveur');
  }
  
  const corsTest = results.tests.find(t => t.name === 'CORS Check');
  if (corsTest?.status === 'FAIL') {
    recommendations.push('🚫 Problème CORS détecté. Configuration serveur requise');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('✅ Aucun problème détecté. L\'erreur peut être temporaire');
  }
  
  return recommendations;
};
