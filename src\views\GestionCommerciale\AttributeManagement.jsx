import React, { useEffect, useState } from 'react';
import {
  fetchAttributeGroups,
  createAttributeGroup,
  updateAttributeGroup,
  deleteAttributeGroup,
  fetchAttributes,
  createAttribute,
  updateAttribute,
  deleteAttribute
} from '../../services/attributeService';
import { Container, Row, Col, Card, Button, Form, Table, Alert, Spinner, Badge, Modal, Tabs, Tab } from 'react-bootstrap';
import { Box, Typography } from '@mui/material';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaLayerGroup, FaTags } from 'react-icons/fa';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';
import MainCard from '../../ui-component/cards/MainCard';
import StandardButton from '../../components/StandardButton';
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';

export default function AttributeManagement() {
  // Columns for attribute groups table
  const attributeGroupsColumns = [
    { id: 'id', label: 'ID', minWidth: 60 },
    { id: 'name', label: 'Nom', minWidth: 200 },
    { id: 'description', label: 'Description', minWidth: 300 },
    { id: 'attributes', label: 'Attributs', minWidth: 120 },
    { id: 'actions', label: 'Actions', minWidth: 200, align: 'center' }
  ];

  // Columns for attributes table
  const attributesColumns = [
    { id: 'id', label: 'ID', minWidth: 50 },
    { id: 'name', label: 'Nom', minWidth: 150 },
    { id: 'type', label: 'Type', minWidth: 100 },
    { id: 'group', label: 'Groupe', minWidth: 120 },
    { id: 'properties', label: 'Propriétés', minWidth: 180 },
    { id: 'actions', label: 'Actions', minWidth: 200, align: 'center' }
  ];

  // State for attribute groups
  const [attributeGroups, setAttributeGroups] = useState([]);
  const [groupLoading, setGroupLoading] = useState(false);

  // State for attributes
  const [attributes, setAttributes] = useState([]);
  const [attributeLoading, setAttributeLoading] = useState(false);

  // Modal states
  const [showGroupModal, setShowGroupModal] = useState(false);
  const [showAttributeModal, setShowAttributeModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Modal action states
  const [modalAction, setModalAction] = useState('create'); // 'create' or 'edit'
  const [deleteTarget, setDeleteTarget] = useState({ type: '', id: null, name: '' });

  // Form states
  const [groupForm, setGroupForm] = useState({ name: '', description: '' });
  const [attributeForm, setAttributeForm] = useState({
    name: '',
    description: '',
    type: 'select',
    attribute_group_id: '',
    filtrable: false,
    comparable: false,
    obligatoire: false,
    sous_categories: []
  });
  // Editing states
  const [editingGroupId, setEditingGroupId] = useState(null);
  const [editingAttributeId, setEditingAttributeId] = useState(null);

  // Submission states
  const [submitting, setSubmitting] = useState(false);

  // UI state
  const [activeTab, setActiveTab] = useState('groups');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Load attribute groups
  const loadAttributeGroups = async () => {
    setGroupLoading(true);
    setError('');
    try {
      const data = await fetchAttributeGroups();
      setAttributeGroups(data);
    } catch (e) {
      setError(e.message);
    }
    setGroupLoading(false);
  };

  // Load attributes
  const loadAttributes = async (groupId = null) => {
    setAttributeLoading(true);
    setError('');
    try {
      const data = await fetchAttributes(groupId);
      setAttributes(data);
    } catch (e) {
      setError(e.message);
    }
    setAttributeLoading(false);
  };

  // Initial data load
  useEffect(() => {
    loadAttributeGroups();
    loadAttributes();
  }, []);

  // Handle group form changes
  const handleGroupChange = (e) => {
    const { name, value } = e.target;
    setGroupForm({ ...groupForm, [name]: value });
  };

  // Handle attribute form changes
  const handleAttributeChange = (e) => {
    const { name, value } = e.target;
    setAttributeForm({ ...attributeForm, [name]: value });
  };

  // Modal handlers for groups
  const handleCreateGroup = () => {
    setModalAction('create');
    setEditingGroupId(null);
    setGroupForm({ name: '', description: '' });
    setShowGroupModal(true);
  };

  const handleEditGroup = (group) => {
    setModalAction('edit');
    setEditingGroupId(group.id);
    setGroupForm({
      name: group.name,
      description: group.description || ''
    });
    setShowGroupModal(true);
  };

  const handleGroupSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      if (modalAction === 'edit' && editingGroupId) {
        await updateAttributeGroup(editingGroupId, groupForm);
        setSuccess("Groupe d'attributs mis à jour avec succès");
      } else {
        await createAttributeGroup(groupForm);
        setSuccess("Groupe d'attributs créé avec succès");
      }

      setShowGroupModal(false);
      setGroupForm({ name: '', description: '' });
      setEditingGroupId(null);
      loadAttributeGroups();
    } catch (e) {
      setError(e.message);
    }

    setSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Modal handlers for attributes
  const handleCreateAttribute = () => {
    setModalAction('create');
    setEditingAttributeId(null);
    setAttributeForm({
      name: '',
      description: '',
      type: 'select',
      attribute_group_id: '',
      filtrable: false,
      comparable: false,
      obligatoire: false,
      sous_categories: []
    });
    setShowAttributeModal(true);
  };

  const handleEditAttribute = (attribute) => {
    setModalAction('edit');
    setEditingAttributeId(attribute.id);
    setAttributeForm({
      name: attribute.name,
      description: attribute.description || '',
      type: attribute.type || 'select',
      attribute_group_id: attribute.attribute_group_id || '',
      filtrable: attribute.filtrable || false,
      comparable: attribute.comparable || false,
      obligatoire: attribute.obligatoire || false,
      sous_categories: attribute.sous_categories || []
    });
    setShowAttributeModal(true);
  };

  const handleAttributeSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      if (modalAction === 'edit' && editingAttributeId) {
        await updateAttribute(editingAttributeId, attributeForm);
        setSuccess('Attribut mis à jour avec succès');
      } else {
        await createAttribute(attributeForm);
        setSuccess('Attribut créé avec succès');
      }

      setShowAttributeModal(false);
      setAttributeForm({
        name: '',
        description: '',
        type: 'select',
        attribute_group_id: '',
        filtrable: false,
        comparable: false,
        obligatoire: false,
        sous_categories: []
      });
      setEditingAttributeId(null);
      loadAttributes();
    } catch (e) {
      setError(e.message);
    }

    setSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Delete handlers
  const handleDeleteGroup = (group) => {
    setDeleteTarget({ type: 'group', id: group.id, name: group.name });
    setShowDeleteModal(true);
  };

  const handleDeleteAttribute = (attribute) => {
    setDeleteTarget({ type: 'attribute', id: attribute.id, name: attribute.name });
    setShowDeleteModal(true);
  };

  const handleDelete = async () => {
    setSubmitting(true);
    setError('');

    try {
      const { type, id } = deleteTarget;

      if (type === 'group') {
        await deleteAttributeGroup(id);
        loadAttributeGroups();
        setSuccess("Groupe d'attributs supprimé avec succès");
      } else if (type === 'attribute') {
        await deleteAttribute(id);
        loadAttributes();
        setSuccess('Attribut supprimé avec succès');
      }
    } catch (e) {
      setError(e.message);
    }

    setSubmitting(false);
    setShowDeleteModal(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Render cell functions for attribute groups table
  const renderAttributeGroupsCell = (column, row) => {
    switch (column.id) {
      case 'id':
        return (
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {row.id}
          </Typography>
        );

      case 'name':
        return (
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {row.name}
          </Typography>
        );

      case 'description':
        return (
          <Typography
            variant="body2"
            sx={{
              maxWidth: 300,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              color: row.description ? COLORS.text.primary : COLORS.text.secondary,
              fontStyle: row.description ? 'normal' : 'italic'
            }}
          >
            {row.description || 'Aucune description'}
          </Typography>
        );

      case 'attributes':
        return row.attributes && row.attributes.length > 0 ? (
          <Box
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              bgcolor: COLORS.info.light,
              color: COLORS.info.main
            }}
          >
            <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
              {row.attributes.length} attributs
            </Typography>
          </Box>
        ) : (
          <Typography variant="body2" sx={{ color: COLORS.text.secondary }}>
            Aucun attribut
          </Typography>
        );

      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton
              variant="outline"
              size="small"
              onClick={() => handleEditGroup(row)}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaPencilAlt />
            </StandardButton>
            <StandardButton
              variant="error"
              size="small"
              onClick={() => handleDeleteGroup(row)}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaTrashAlt />
            </StandardButton>
          </Box>
        );

      default:
        return row[column.id];
    }
  };

  // Render cell functions for attributes table
  const renderAttributesCell = (column, row) => {
    switch (column.id) {
      case 'id':
        return (
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {row.id}
          </Typography>
        );

      case 'name':
        return (
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {row.name}
          </Typography>
        );

      case 'type':
        const typeLabels = {
          text: 'Texte',
          number: 'Nombre',
          boolean: 'Booléen',
          select: 'Liste'
        };
        return (
          <Box
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              bgcolor: COLORS.info.light,
              color: COLORS.info.main
            }}
          >
            <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
              {typeLabels[row.type] || row.type}
            </Typography>
          </Box>
        );

      case 'group':
        const group = attributeGroups.find((g) => g.id === row.attribute_group_id);
        return group ? (
          <Box
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              bgcolor: COLORS.primary.light,
              color: COLORS.primary.main
            }}
          >
            <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
              {group.name}
            </Typography>
          </Box>
        ) : (
          <Typography variant="body2" sx={{ color: COLORS.text.secondary, fontStyle: 'italic' }}>
            Aucun groupe
          </Typography>
        );

      case 'properties':
        const properties = [];
        if (row.filtrable) properties.push({ label: 'Filtrable', color: COLORS.info });
        if (row.comparable) properties.push({ label: 'Comparable', color: COLORS.success });
        if (row.obligatoire) properties.push({ label: 'Obligatoire', color: COLORS.warning });

        return properties.length > 0 ? (
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
            {properties.map((prop, index) => (
              <Box
                key={index}
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  px: 1,
                  py: 0.25,
                  borderRadius: 0.5,
                  bgcolor: prop.color.light,
                  color: prop.color.main
                }}
              >
                <Typography variant="caption" sx={{ fontSize: '0.7rem', fontWeight: 'medium' }}>
                  {prop.label}
                </Typography>
              </Box>
            ))}
          </Box>
        ) : (
          <Typography variant="body2" sx={{ color: COLORS.text.secondary, fontStyle: 'italic' }}>
            Aucune
          </Typography>
        );

      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton
              variant="outline"
              size="small"
              onClick={() => handleEditAttribute(row)}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaPencilAlt />
            </StandardButton>
            <StandardButton
              variant="error"
              size="small"
              onClick={() => handleDeleteAttribute(row)}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaTrashAlt />
            </StandardButton>
          </Box>
        );

      default:
        return row[column.id];
    }
  };

  return (
    <MainCard>
      <Box sx={{ width: '100%' }}>
        {/* Breadcrumb - Design System Style */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.sm
            }}
          >
            Accueil &gt; Gestion des Attributs
          </Typography>
        </Box>

        {/* Header - Design System Style */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h3"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.text.dark,
              mb: 1
            }}
          >
            Gestion des Attributs
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.md
            }}
          >
            Gérez les attributs et leurs valeurs pour les produits
          </Typography>
        </Box>

        {/* Error and Success Messages */}
        {error && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        )}
        {success && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="success">{success}</Alert>
          </Box>
        )}

        {/* Tabs - Design System Style */}
        <StandardCard sx={{ mb: 3 }}>
          <Box sx={{ p: 0 }}>
            <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-0 nav-tabs-custom" fill>
              <Tab
                eventKey="groups"
                title={
                  <span>
                    <FaLayerGroup className="me-2" />
                    Groupes d'Attributs
                  </span>
                }
              />
              <Tab
                eventKey="attributes"
                title={
                  <span>
                    <FaTags className="me-2" />
                    Attributs
                  </span>
                }
              />
            </Tabs>
          </Box>
        </StandardCard>

        {/* Tab Content */}
        {activeTab === 'groups' && (
          <>
            {/* Groups Header */}
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography
                  variant="h5"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontSize: TYPOGRAPHY.fontSize.lg,
                    fontWeight: TYPOGRAPHY.fontWeight.semibold,
                    color: COLORS.text.dark
                  }}
                >
                  Liste des groupes d'attributs ({attributeGroups.length})
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    color: COLORS.text.secondary,
                    mt: 0.5
                  }}
                >
                  {attributeGroups.length} groupe{attributeGroups.length !== 1 ? 's' : ''} d'attributs
                </Typography>
              </Box>
              <StandardButton variant="primary" onClick={handleCreateGroup} startIcon={<FaPlus />} size="medium">
                Ajouter un groupe
              </StandardButton>
            </Box>

            {/* Groups Table */}
            <StandardTable
              columns={attributeGroupsColumns}
              data={attributeGroups}
              loading={groupLoading}
              error={error}
              emptyMessage="Aucun groupe d'attributs trouvé. Créez votre premier groupe pour commencer."
              renderCell={renderAttributeGroupsCell}
              hover={true}
            />
          </>
        )}

        {activeTab === 'attributes' && (
          <>
            {/* Attributes Header */}
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography
                  variant="h5"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontSize: TYPOGRAPHY.fontSize.lg,
                    fontWeight: TYPOGRAPHY.fontWeight.semibold,
                    color: COLORS.text.dark
                  }}
                >
                  Liste des attributs ({attributes.length})
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    color: COLORS.text.secondary,
                    mt: 0.5
                  }}
                >
                  {attributes.length} attribut{attributes.length !== 1 ? 's' : ''}
                </Typography>
              </Box>
              <StandardButton variant="primary" onClick={handleCreateAttribute} startIcon={<FaPlus />} size="medium">
                Ajouter un attribut
              </StandardButton>
            </Box>

            {/* Attributes Table */}
            <StandardTable
              columns={attributesColumns}
              data={attributes}
              loading={attributeLoading}
              error={error}
              emptyMessage="Aucun attribut trouvé. Créez votre premier attribut pour commencer."
              renderCell={renderAttributesCell}
              hover={true}
            />
          </>
        )}

        {/* Delete Confirmation Modal */}
        <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
          <Modal.Header closeButton>
            <Modal.Title>Confirmer la suppression</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {deleteTarget.type === 'group' && (
              <p>
                Êtes-vous sûr de vouloir supprimer le groupe d'attributs <strong>"{deleteTarget.name}"</strong> ?<br />
                <small className="text-muted">Cette action supprimera également tous les attributs associés à ce groupe.</small>
              </p>
            )}
            {deleteTarget.type === 'attribute' && (
              <p>
                Êtes-vous sûr de vouloir supprimer l'attribut <strong>"{deleteTarget.name}"</strong> ?<br />
                <small className="text-muted">Cette action supprimera également toutes les valeurs associées à cet attribut.</small>
              </p>
            )}
          </Modal.Body>
          <Modal.Footer>
            <StandardButton variant="secondary" onClick={() => setShowDeleteModal(false)} disabled={submitting}>
              Annuler
            </StandardButton>
            <StandardButton variant="error" onClick={handleDelete} disabled={submitting}>
              {submitting ? 'Suppression...' : 'Supprimer'}
            </StandardButton>
          </Modal.Footer>
        </Modal>

        {/* Group Modal */}
        <Modal show={showGroupModal} onHide={() => setShowGroupModal(false)} size="lg" centered>
          <Modal.Header closeButton>
            <Modal.Title>
              <FaLayerGroup className="me-2" />
              {modalAction === 'edit' ? "Modifier le groupe d'attributs" : "Ajouter un nouveau groupe d'attributs"}
            </Modal.Title>
          </Modal.Header>
          <Form onSubmit={handleGroupSubmit}>
            <Modal.Body>
              <Row className="g-3">
                <Col xs={12}>
                  <Form.Group controlId="groupName">
                    <Form.Label className="fw-medium">Nom du groupe</Form.Label>
                    <Form.Control
                      name="name"
                      value={groupForm.name}
                      onChange={handleGroupChange}
                      placeholder="Ex: Caractéristiques techniques"
                      required
                      disabled={submitting}
                      className="rounded-3 border-2"
                    />
                  </Form.Group>
                </Col>
                <Col xs={12}>
                  <Form.Group controlId="groupDescription">
                    <Form.Label className="fw-medium">Description</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      name="description"
                      value={groupForm.description}
                      onChange={handleGroupChange}
                      placeholder="Description du groupe d'attributs"
                      disabled={submitting}
                      className="rounded-3 border-2"
                    />
                  </Form.Group>
                </Col>
              </Row>
            </Modal.Body>
            <Modal.Footer>
              <StandardButton variant="secondary" onClick={() => setShowGroupModal(false)} disabled={submitting}>
                Annuler
              </StandardButton>
              <StandardButton
                type="submit"
                variant="primary"
                disabled={submitting}
                startIcon={submitting ? null : modalAction === 'edit' ? <FaPencilAlt /> : <FaPlus />}
              >
                {submitting ? (modalAction === 'edit' ? 'Modification...' : 'Création...') : modalAction === 'edit' ? 'Modifier' : 'Créer'}
              </StandardButton>
            </Modal.Footer>
          </Form>
        </Modal>

        {/* Attribute Modal */}
        <Modal
          show={showAttributeModal}
          onHide={() => setShowAttributeModal(false)}
          size="md"
          centered
          className="professional-modal"
          dialogClassName="professional-modal-dialog"
        >
          <Modal.Header closeButton className="professional-modal-header bg-primary text-white">
            <Modal.Title>
              <FaTags className="me-2" />
              {modalAction === 'edit' ? 'Modifier un attribut' : 'Ajouter un nouvel attribut'}
            </Modal.Title>
          </Modal.Header>
          <Form onSubmit={handleAttributeSubmit}>
            <Modal.Body className="professional-modal-body" style={{ maxHeight: '70vh', overflowY: 'auto' }}>
              <Row className="g-3">
                <Col xs={12} md={6}>
                  <Form.Group controlId="attributeName">
                    <Form.Label className="fw-medium">Nom de l'attribut</Form.Label>
                    <Form.Control
                      name="name"
                      value={attributeForm.name}
                      onChange={handleAttributeChange}
                      placeholder="Ex: Couleur"
                      required
                      disabled={submitting}
                      className="rounded-3 border-2"
                    />
                  </Form.Group>
                </Col>
                <Col xs={12} md={6}>
                  <Form.Group controlId="attributeType">
                    <Form.Label className="fw-medium">Type d'attribut</Form.Label>
                    <Form.Select
                      name="type"
                      value={attributeForm.type}
                      onChange={handleAttributeChange}
                      required
                      disabled={submitting}
                      className="rounded-3 border-2"
                    >
                      <option value="text">Texte</option>
                      <option value="number">Nombre</option>
                      <option value="boolean">Booléen</option>
                      <option value="select">Liste de valeurs</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col xs={12}>
                  <Form.Group controlId="attributeGroup">
                    <Form.Label className="fw-medium">Groupe d'attributs</Form.Label>
                    <Form.Select
                      name="attribute_group_id"
                      value={attributeForm.attribute_group_id}
                      onChange={handleAttributeChange}
                      disabled={submitting || attributeGroups.length === 0}
                      className="rounded-3 border-2"
                    >
                      <option value="">Aucun groupe</option>
                      {attributeGroups.map((group) => (
                        <option key={group.id} value={group.id}>
                          {group.name}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col xs={12}>
                  <Form.Group controlId="attributeDescription">
                    <Form.Label className="fw-medium">Description</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      name="description"
                      value={attributeForm.description}
                      onChange={handleAttributeChange}
                      placeholder="Description de l'attribut"
                      disabled={submitting}
                      className="rounded-3 border-2"
                    />
                  </Form.Group>
                </Col>
                <Col xs={12}>
                  <div className="border rounded-3 p-3 bg-light">
                    <h6 className="mb-3">Propriétés de l'attribut</h6>
                    <Row>
                      <Col xs={12} md={4}>
                        <Form.Group controlId="attributeFiltrable">
                          <Form.Check
                            type="checkbox"
                            label="Filtrable"
                            name="filtrable"
                            checked={attributeForm.filtrable}
                            onChange={(e) => setAttributeForm({ ...attributeForm, filtrable: e.target.checked })}
                            disabled={submitting}
                          />
                          <Form.Text className="text-muted">Permet de filtrer les produits par cet attribut</Form.Text>
                        </Form.Group>
                      </Col>
                      <Col xs={12} md={4}>
                        <Form.Group controlId="attributeComparable">
                          <Form.Check
                            type="checkbox"
                            label="Comparable"
                            name="comparable"
                            checked={attributeForm.comparable}
                            onChange={(e) => setAttributeForm({ ...attributeForm, comparable: e.target.checked })}
                            disabled={submitting}
                          />
                          <Form.Text className="text-muted">Permet de comparer les produits par cet attribut</Form.Text>
                        </Form.Group>
                      </Col>
                      <Col xs={12} md={4}>
                        <Form.Group controlId="attributeObligatoire">
                          <Form.Check
                            type="checkbox"
                            label="Obligatoire"
                            name="obligatoire"
                            checked={attributeForm.obligatoire}
                            onChange={(e) => setAttributeForm({ ...attributeForm, obligatoire: e.target.checked })}
                            disabled={submitting}
                          />
                          <Form.Text className="text-muted">Rend cet attribut obligatoire pour les produits</Form.Text>
                        </Form.Group>
                      </Col>
                    </Row>
                  </div>
                </Col>
              </Row>
            </Modal.Body>
            <Modal.Footer className="professional-modal-footer">
              <StandardButton variant="secondary" onClick={() => setShowAttributeModal(false)} disabled={submitting}>
                Annuler
              </StandardButton>
              <StandardButton
                type="submit"
                variant="primary"
                disabled={submitting}
                startIcon={submitting ? null : modalAction === 'edit' ? <FaPencilAlt /> : <FaPlus />}
              >
                {submitting ? (modalAction === 'edit' ? 'Modification...' : 'Création...') : modalAction === 'edit' ? 'Modifier' : 'Créer'}
              </StandardButton>
            </Modal.Footer>
          </Form>
        </Modal>

        {/* Custom CSS for styling */}
        <style jsx="true">{`
          .nav-tabs-custom .nav-link {
            color: #495057;
            font-weight: 500;
            padding: 1rem 1.5rem;
            border-radius: 0;
            border: none;
            border-bottom: 3px solid transparent;
          }
          .nav-tabs-custom .nav-link.active {
            color: #2196f3;
            background: transparent;
            border-bottom: 3px solid #2196f3;
          }
          .nav-tabs-custom .nav-link:hover:not(.active) {
            border-bottom: 3px solid #e9ecef;
          }
        `}</style>
      </Box>
    </MainCard>
  );
}
